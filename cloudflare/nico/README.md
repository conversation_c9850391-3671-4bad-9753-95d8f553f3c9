# Template: worker-typescript

[![Deploy with Workers](https://deploy.workers.cloudflare.com/button)](https://deploy.workers.cloudflare.com/?url=https://github.com/cloudflare/workers-sdk/tree/main/templates/worker-typescript)

A batteries included template for kick starting a TypeScript Cloudflare worker project.

## Setup

To create a `my-project` directory using this template, run:

```sh
$ npx wrangler generate my-project worker-typescript
# or
$ yarn wrangler generate my-project worker-typescript
# or
$ pnpm wrangler generate my-project worker-typescript
```
