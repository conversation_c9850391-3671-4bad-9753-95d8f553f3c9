import { REWARDS_TABLE_NAME } from '../constants';
import { RewardCreateType, RewardType } from '../types';
import { generateULIDId } from '../utils';

export class RewardRepository {
	sql: SqlStorage;

	constructor(sql: SqlStorage) {
		this.sql = sql;
	}

	async create(rewardData: RewardCreateType): Promise<RewardType> {
		const query = `INSERT INTO ${REWARDS_TABLE_NAME} (id, name, description, accumulationMode, target, value, currency)
                   VALUES (?, ?, ?, ?, ?, ?, ?)
                   RETURNING *`;
		return this.sql
			.exec<RewardType>(
				query,
				generateULIDId(REWARDS_TABLE_NAME),
				rewardData.name,
				rewardData.description,
				rewardData.accumulationMode,
				rewardData.target,
				rewardData.value,
				rewardData.currency
			)
			.one();
	}

	async getById(rewardId: string): Promise<RewardType> {
		const query = `SELECT * FROM ${REWARDS_TABLE_NAME} WHERE id = ?`;
		return this.sql.exec<RewardType>(query, [rewardId]).one();
	}

	async getAll(): Promise<RewardType[]> {
		const query = `SELECT * FROM ${REWARDS_TABLE_NAME}`;
		return this.sql.exec<RewardType>(query).toArray();
	}

	async update(rewardId: string, updateData: Partial<RewardType>): Promise<RewardType> {
		const fieldsToUpdate = [];
		const valuesToUpdate = [];

		if (updateData.name) {
			fieldsToUpdate.push('name = ?');
			valuesToUpdate.push(updateData.name);
		}

		if (updateData.description) {
			fieldsToUpdate.push('description = ?');
			valuesToUpdate.push(updateData.description);
		}

		if (updateData.currentProgress != null) {
			fieldsToUpdate.push('currentProgress = ?');
			valuesToUpdate.push(updateData.currentProgress);
		}

		if (fieldsToUpdate.length === 0) {
			throw new Error('No fields to update');
		}

		const query = `UPDATE ${REWARDS_TABLE_NAME} SET ${fieldsToUpdate.join(', ')} WHERE id = ? RETURNING *`;
		valuesToUpdate.push(rewardId);

		return this.sql.exec<RewardType>(query, ...valuesToUpdate).one();
	}

	async delete(rewardId: string): Promise<{ message: string }> {
		const query = `DELETE FROM ${REWARDS_TABLE_NAME} WHERE id = ?`;
		this.sql.exec(query, [rewardId]);
		return { message: 'REWARD_DELETED_SUCCESSFULLY' };
	}
}
