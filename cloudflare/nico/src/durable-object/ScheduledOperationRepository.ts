import { SCHEDULED_OPERATIONS_TABLE } from '../constants';
import {
	ScheduledOperation,
	ScheduledOperationCreateType,
	ScheduledOperationRecordType,
	ScheduledOperationStatus,
} from '../types';
import { generateULIDId } from '../utils';

const recordToResponseTransformer = (record: ScheduledOperationRecordType) => {
	const isOverdue = new Date(record.runAt).getTime() < Date.now();
	const response: ScheduledOperation = {
		...record,
		isOverdue,
		data: record.data ? JSON.parse(record.data) : null,
	};
	return response;
};

export class ScheduledOperationsRepository {
	sql: SqlStorage;

	constructor(sql: SqlStorage) {
		this.sql = sql;
	}

	async create(scheduleddata: ScheduledOperationCreateType): Promise<ScheduledOperation> {
		try {
			const query = `
			INSERT INTO ${SCHEDULED_OPERATIONS_TABLE}
			(id, type, status, domainId, runAt, createdDate, updatedDate, data)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING *`;
			const currentDate = new Date().toISOString();
			const result = this.sql
				.exec<ScheduledOperationRecordType>(
					query,
					generateULIDId('scheduled-operation'),
					scheduleddata.type,
					scheduleddata.status,
					scheduleddata.domainId,
					scheduleddata.runAt,
					currentDate,
					currentDate,
					scheduleddata.data
				)
				.toArray();
			if (result.length === 0) {
				throw new Error('No rows returned after INSERT operation.');
			}
			return recordToResponseTransformer(result[0]);
		} catch (error) {
			console.error('Error running Scheduled Operation create', error);
			throw error;
		}
	}

	async getById(operationId: string): Promise<ScheduledOperation | null> {
		try {
			const query = `SELECT * FROM ${SCHEDULED_OPERATIONS_TABLE} WHERE id = ?`;
			const records = this.sql.exec<ScheduledOperationRecordType>(query, [operationId]).toArray();
			return records.length > 0 ? recordToResponseTransformer(records[0]) : null;
		} catch (error) {
			console.error('Error running Scheduled Operation getById', error);
			return null;
		}
	}
	async getAll(status?: ScheduledOperationStatus): Promise<ScheduledOperation[]> {
		try {
			let query = `SELECT * FROM ${SCHEDULED_OPERATIONS_TABLE}`;
			if (status) {
				query += ` WHERE status = ?`;
			}

			query += ` ORDER BY runAt ASC`;

			const records = status
				? this.sql.exec<ScheduledOperationRecordType>(query, [status]).toArray()
				: this.sql.exec<ScheduledOperationRecordType>(query).toArray();

			// Transform and return the results
			return records.map(recordToResponseTransformer);
		} catch (error) {
			console.error('Error running Scheduled Operation getAll', error);
			return [];
		}
	}

	async getPendingOperation(beforeTime: number): Promise<ScheduledOperation | null> {
		try {
			const targetTime = new Date(beforeTime).toISOString();
			const query = `
			SELECT * FROM ${SCHEDULED_OPERATIONS_TABLE}
			WHERE status = 'pending' AND runAt <= ?
			ORDER BY runAt ASC limit 1`;
			const records = this.sql.exec<ScheduledOperationRecordType>(query, [targetTime]).toArray();
			if (records.length > 0) {
				return recordToResponseTransformer(records[0]);
			}
			return null;
		} catch (error) {
			console.error('Error running Scheduled Operation getPendingOperations', error);
			return null;
		}
	}

	async getAllByType(type: ScheduledOperation['type']): Promise<ScheduledOperation[]> {
		try {
			const query = `SELECT * FROM ${SCHEDULED_OPERATIONS_TABLE} WHERE type = ? ORDER BY runAt ASC`;
			const records = this.sql.exec<ScheduledOperationRecordType>(query, [type]).toArray();

			return records.map(recordToResponseTransformer);
		} catch (error) {
			console.error('Error running Scheduled Operation getAllByType', error);
			return [];
		}
	}

	async getLastPendingOperationByType(
		type: ScheduledOperation['type']
	): Promise<ScheduledOperation | null> {
		try {
			const query = `SELECT * FROM ${SCHEDULED_OPERATIONS_TABLE} WHERE type = ? and status = 'pending' ORDER BY runAt DESC LIMIT 1`;
			const records = this.sql.exec<ScheduledOperationRecordType>(query, [type]).toArray();
			return records.length > 0 ? recordToResponseTransformer(records[0]) : null;
		} catch (error) {
			console.error('Error running Scheduled Operation getLastPendingOperationByType', error);
			return null;
		}
	}

	async getScheduledOperationAt(runAt: number): Promise<ScheduledOperation | null> {
		try {
			const targetTime = new Date(runAt).toISOString();
			const query = `
			SELECT * FROM ${SCHEDULED_OPERATIONS_TABLE}
			WHERE runAt = ?
			ORDER BY runAt ASC`;
			const records = this.sql.exec<ScheduledOperationRecordType>(query, [targetTime]).toArray();
			if (records.length === 0) {
				return null;
			}
			return recordToResponseTransformer(records[0]);
		} catch (error) {
			console.error('Error running Scheduled Operation getPendingOperations', error);
			return null;
		}
	}

	async cleanupCompletedOperationsBefore90Days(): Promise<void> {
		try {
			const date90DaysAgo = new Date();
			date90DaysAgo.setDate(date90DaysAgo.getDate() - 90);
			const formattedDate = date90DaysAgo.toISOString().split('T')[0]; // Extract the date part (YYYY-MM-DD)

			const query = `
				DELETE FROM ${SCHEDULED_OPERATIONS_TABLE}
				WHERE status = 'completed' AND updatedDate < ?`;

			this.sql.exec(query, [formattedDate]);
		} catch (error) {
			console.error('Error running Scheduled Operations cleanup', error);
			throw error;
		}
	}

	async update(
		operationId: string,
		updateData: Partial<ScheduledOperationCreateType>
	): Promise<ScheduledOperation> {
		try {
			const fieldsToUpdate = [];
			const valuesToUpdate = [];
			const currentDate = new Date().toISOString();

			if (updateData.type) {
				fieldsToUpdate.push('type = ?');
				valuesToUpdate.push(updateData.type);
			}

			if (updateData.status) {
				fieldsToUpdate.push('status = ?');
				valuesToUpdate.push(updateData.status);
			}

			if (updateData.runAt) {
				fieldsToUpdate.push('runAt = ?');
				valuesToUpdate.push(new Date(updateData.runAt).toISOString());
			}

			if (updateData.data) {
				fieldsToUpdate.push('data = ?');
				valuesToUpdate.push(updateData.data);
			}

			fieldsToUpdate.push('updatedDate = ?');
			valuesToUpdate.push(currentDate);

			if (fieldsToUpdate.length === 1) {
				throw new Error('No fields to update');
			}

			const query = `UPDATE ${SCHEDULED_OPERATIONS_TABLE} SET ${fieldsToUpdate.join(
				', '
			)} WHERE id = ? RETURNING *`;
			valuesToUpdate.push(operationId);

			const records = this.sql
				.exec<ScheduledOperationRecordType>(query, ...valuesToUpdate)
				.toArray();
			if (records.length === 0) {
				throw new Error('No rows returned after UPDATE operation.');
			}
			return recordToResponseTransformer(records[0]);
		} catch (error) {
			console.error('Error running Scheduled Operation update', error);
			throw error;
		}
	}

	async deleteByDomainId(deleteByDomainId: string): Promise<void> {
		const query = `DELETE FROM ${SCHEDULED_OPERATIONS_TABLE} WHERE domainId = ?`;
		this.sql.exec(query, [deleteByDomainId]);
	}

	async getLatestPendingOperationByType(type: ScheduledOperation['type']): Promise<ScheduledOperation | null> {
		try {
			const query = `
			SELECT * FROM ${SCHEDULED_OPERATIONS_TABLE}
			WHERE type = ? AND status = 'pending' AND runAt >= datetime('now')
			ORDER BY runAt DESC
			LIMIT 1`;
			const records = this.sql.exec<ScheduledOperationRecordType>(query, [type]).toArray();
			return records.length > 0 ? recordToResponseTransformer(records[0]) : null;
		} catch (error) {
			console.error('Error running Scheduled Operation getLatestByType', error);
			return null;
		}
	}

	async getNextScheduledOperation(): Promise<ScheduledOperation | null> {
		try {
			const query = `
			SELECT * FROM ${SCHEDULED_OPERATIONS_TABLE}
			WHERE status = 'pending'
			ORDER BY runAt ASC
			LIMIT 1
		`;
			const records = this.sql.exec<ScheduledOperationRecordType>(query).toArray();
			return records.length > 0 ? recordToResponseTransformer(records[0]) : null;
		} catch (error) {
			console.error('Error running getNextScheduledOperation', error);
			return null;
		}
	}
}
