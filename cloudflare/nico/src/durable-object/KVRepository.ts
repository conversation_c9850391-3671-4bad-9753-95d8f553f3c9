import { KV_STORAGE_TABLE } from "../constants/db/kv-storage";

export class KVRepository {
	sql: SqlStorage;

	constructor(sql: SqlStorage) {
		this.sql = sql;
	}

	async put<T>(key: string, value: T): Promise<T | null> {
		try {
			const query = `
			INSERT INTO ${KV_STORAGE_TABLE} (key, value)
			VALUES (?, ?)
			ON CONFLICT(key) DO UPDATE SET value = excluded.value
			RETURNING key, value
		`;
			const result = this.sql
				.exec<{ key: string; value: string }>(
					query,
					key,
					typeof value === 'object' ? JSON.stringify(value) : value
				)
				.one();
			try {
				return JSON.parse(result.value) as T;
			} catch (error) {
				return result.value as T;
			}
		} catch (error) {
			console.error('Error putting item from kv_storage', error);
			return null;
		}
	}

	async get<T = string>(key: string): Promise<T | null> {
		try {
			const query = `SELECT key, value FROM ${KV_STORAGE_TABLE} WHERE key = ?`;
			const result = this.sql.exec<{ key: string; value: string }>(query, key).one();
			if (!result) return null;

			try {
				return JSON.parse(result.value) as T;
			} catch {
				return result.value as T;
			}
		} catch (error) {
			return null;
		}
	}

	async delete(key: string): Promise<{ message: string }> {
		const query = `DELETE FROM ${KV_STORAGE_TABLE} WHERE key = ?`;
		this.sql.exec(query, key);
		return { message: 'KEY_DELETED_SUCCESSFULLY' };
	}
}
