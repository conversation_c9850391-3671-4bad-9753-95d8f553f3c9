import { GROUP_CONTEXT_TABLE } from '../constants';
import { GroupContextCreateType, GroupContextRecordType } from '../types';
import { generateULIDId } from '../utils';

export class GroupContextRepository {
	sql: SqlStorage;

	constructor(sql: SqlStorage) {
		this.sql = sql;
	}

	async deleteById(contextId: string): Promise<void> {
		const query = `DELETE FROM ${GROUP_CONTEXT_TABLE} WHERE id = ?`;
		this.sql.exec(query, [contextId]);
	}

	async getActiveContextsBeforeDate(timeInMillis: number): Promise<GroupContextRecordType[]> {
		const targetDate = new Date(Date.now() - timeInMillis).toISOString();

		const query = `
			SELECT * FROM ${GROUP_CONTEXT_TABLE}
			WHERE status = 'active' AND createdDate <= ?
			ORDER BY createdDate DESC
		`;

		return this.sql.exec<GroupContextRecordType>(query, [targetDate]).toArray();
	}

	async getById(contextId: string): Promise<GroupContextRecordType[]> {
		const query = `SELECT * FROM ${GROUP_CONTEXT_TABLE} WHERE id = ?`;
		return this.sql.exec<GroupContextRecordType>(query, [contextId]).toArray();
	}

	async getAllByGroupId(groupId: string): Promise<GroupContextRecordType[]> {
		const query = `SELECT * FROM ${GROUP_CONTEXT_TABLE} WHERE groupId = ? ORDER BY createdDate DESC`;
		return this.sql.exec<GroupContextRecordType>(query, [groupId]).toArray();
	}

	async getAll(): Promise<GroupContextRecordType[]> {
		const query = `SELECT * FROM ${GROUP_CONTEXT_TABLE} ORDER BY createdDate DESC`;
		return this.sql.exec<GroupContextRecordType>(query).toArray();
	}

	async getAllActiveGroupContexts(): Promise<GroupContextRecordType[]> {
		const query = `SELECT * FROM ${GROUP_CONTEXT_TABLE}  where status = 'active' ORDER BY createdDate DESC`;
		return this.sql.exec<GroupContextRecordType>(query).toArray();
	}

	async create(groupContextData: GroupContextCreateType): Promise<GroupContextRecordType[]> {
		const query = `INSERT INTO ${GROUP_CONTEXT_TABLE} (id, type, groupId, status, createdDate, updatedDate, contextData, rewardId) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING *`;
		const currentDate = new Date().toISOString();
		return this.sql
			.exec<GroupContextRecordType>(
				query,
				generateULIDId('task-group-context'),
				groupContextData.type,
				groupContextData.groupId,
				groupContextData.status,
				currentDate,
				currentDate,
				JSON.stringify(groupContextData.contextData),
				groupContextData.rewardId
			)
			.toArray();
	}

	async update(
		contextId: string,
		updateData: Partial<GroupContextCreateType>
	): Promise<GroupContextRecordType[]> {
		const fieldsToUpdate = [];
		const valuesToUpdate = [];
		const currentDate = new Date().toISOString();

		if (updateData.status) {
			fieldsToUpdate.push('status = ?');
			valuesToUpdate.push(updateData.status);
		}

		if (updateData.rewardId) {
			fieldsToUpdate.push('rewardId = ?');
			valuesToUpdate.push(updateData.rewardId);
		}

		if (updateData.contextData) {
			fieldsToUpdate.push('contextData = ?');
			valuesToUpdate.push(JSON.stringify(updateData.contextData));
		}

		fieldsToUpdate.push('updatedDate = ?');
		valuesToUpdate.push(currentDate);

		if (fieldsToUpdate.length === 1) {
			throw new Error('No fields to update');
		}

		const query = `UPDATE ${GROUP_CONTEXT_TABLE} SET ${fieldsToUpdate.join(', ')} WHERE id = ? RETURNING *`;
		valuesToUpdate.push(contextId);

		return this.sql.exec<GroupContextRecordType>(query, ...valuesToUpdate).toArray();
	}

	async getLatestByGroupId(groupId: string): Promise<GroupContextRecordType[]> {
		const query = `SELECT * FROM ${GROUP_CONTEXT_TABLE} WHERE groupId = ? AND (status = 'active' OR status = 'pending-payment') ORDER BY createdDate DESC LIMIT 1`;
		return this.sql.exec<GroupContextRecordType>(query, [groupId]).toArray();
	}
}
