import { ASSIGNMENT_TABLE } from '../constants';
import { AssignmentCreateType, AssignmentType } from '../types';

export class AssignmentRepository {
	sql: SqlStorage;

	constructor(sql: SqlStorage) {
		this.sql = sql;
	}

	async create(
		assignmentData: AssignmentCreateType,
		groupContextId: string
	): Promise<AssignmentType> {
		const query = `INSERT INTO ${ASSIGNMENT_TABLE} (assignmentId, platformId, groupId, groupContextId) VALUES (?, ?, ?, ?) RETURNING *`;
		return this.sql
			.exec<AssignmentType>(
				query,
				assignmentData.assignmentId,
				assignmentData.platformId,
				assignmentData.groupId,
				groupContextId
			)
			.one();
	}

	async getById(assignmentId: string): Promise<AssignmentType> {
		const query = `SELECT * FROM ${ASSIGNMENT_TABLE} WHERE assignmentId = ?`;
		return this.sql.exec<AssignmentType>(query, [assignmentId]).one();
	}

	async update(assignmentId: string, updateData: Partial<AssignmentType>): Promise<AssignmentType> {
		const fieldsToUpdate = [];
		const valuesToUpdate = [];

		if (updateData.groupId) {
			fieldsToUpdate.push('groupId = ?');
			valuesToUpdate.push(updateData.groupId);
		}

		if (fieldsToUpdate.length === 0) {
			throw new Error('No fields to update');
		}

		const query = `UPDATE ${ASSIGNMENT_TABLE} SET ${fieldsToUpdate.join(
			', '
		)} WHERE assignmentId = ? RETURNING *`;
		valuesToUpdate.push(assignmentId);

		return this.sql.exec<AssignmentType>(query, ...valuesToUpdate).one();
	}
}
