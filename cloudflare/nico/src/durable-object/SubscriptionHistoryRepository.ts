import { SUBSCRIPTION_HISTORY_TABLE } from '../constants/db/subscription';
import { SubscriptionHistory, SubscriptionHistoryCreate } from '../types';
import { generateULIDId } from '../utils';

export class SubscriptionHistoryRepository {
	sql: SqlStorage;

	constructor(sql: SqlStorage) {
		this.sql = sql;
	}

	async create(historyData: SubscriptionHistoryCreate): Promise<SubscriptionHistory> {
		try {
			const query = `
		INSERT INTO ${SUBSCRIPTION_HISTORY_TABLE}
		(id, subscriptionId, productId, eventType, eventTimestamp, previousStatus, currentStatus, createdAt)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING *`;
			const currentDate = new Date().toISOString();
			const result = this.sql
				.exec<SubscriptionHistory>(
					query,
					generateULIDId('subscription-history'),
					historyData.subscriptionId,
					historyData.productId,
					historyData.eventType,
					historyData.eventTimestamp,
					historyData.previousStatus ?? null,
					historyData.currentStatus,
					currentDate
				)
				.toArray();

			if (result.length > 0) {
				return result[0];
			}
			throw new Error('No rows returned after INSERT operation.');
		} catch (error) {
			console.error('Error running Subscription History create', error);
			throw error;
		}
	}

	async getBySubscriptionId(subscriptionId: string): Promise<SubscriptionHistory[]> {
		try {
			const query = `SELECT * FROM ${SUBSCRIPTION_HISTORY_TABLE} WHERE subscriptionId = ? ORDER BY eventTimestamp ASC`;
			const records = this.sql.exec<SubscriptionHistory>(query, [subscriptionId]).toArray();
			return records;
		} catch (error) {
			console.error('Error running Subscription History getBySubscriptionId', error);
			return [];
		}
	}

	async getByProductId(productId: string): Promise<any[]> {
		try {
			const query = `SELECT * FROM ${SUBSCRIPTION_HISTORY_TABLE} WHERE productId = ? ORDER BY eventTimestamp ASC`;
			const records = this.sql.exec<SubscriptionHistory>(query, [productId]).toArray();
			return records;
		} catch (error) {
			console.error('Error running Subscription History getByproductId', error);
			return [];
		}
	}
}
