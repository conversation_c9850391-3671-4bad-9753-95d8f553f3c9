import { SUBSCRIPTION_TABLE } from '../constants/db/subscription';
import { Subscription, SubscriptionCreate, SubscriptionRecord } from '../types/subscription';

export class SubscriptionRepository {
	sql: SqlStorage;

	constructor(sql: SqlStorage) {
		this.sql = sql;
	}

	async create(subscriptionData: SubscriptionCreate): Promise<Subscription> {
		try {
			const query = `
				INSERT INTO ${SUBSCRIPTION_TABLE}
				(subscriptionId, productId, billingCycle, status, startedAt, expiresAt, paymentType, isAutoRenewalEnabled, createdAt, updatedAt)
				VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING *`;

			const currentDate = new Date().toISOString();
			const result = this.sql
				.exec<SubscriptionRecord>(
					query,
					subscriptionData.subscriptionId,
					subscriptionData.productId,
					subscriptionData.billingCycle,
					subscriptionData.status,
					subscriptionData.startedAt,
					subscriptionData.expiresAt,
					subscriptionData.paymentType,
					subscriptionData.isAutoRenewalEnabled,
					currentDate,
					currentDate
				)
				.toArray();

			if (result.length === 0) {
				throw new Error('No rows returned after INSERT operation.');
			}

			return {
				...result[0],
				isAutoRenewalEnabled: Boolean(result[0].isAutoRenewalEnabled),
			};
		} catch (error) {
			console.error('Error running Subscription create', error);
			throw Error(`Error running Subscription create ${error}`);
		}
	}

	async getById(subscriptionId: string): Promise<Subscription | null> {
		try {
			const query = `SELECT * FROM ${SUBSCRIPTION_TABLE} WHERE subscriptionId = ?`;
			const records = this.sql.exec<SubscriptionRecord>(query, [subscriptionId]).toArray();
			return records.length > 0
				? { ...records[0], isAutoRenewalEnabled: Boolean(records[0].isAutoRenewalEnabled) }
				: null;
		} catch (error) {
			console.error('Error running Subscription getById', error);
			return null;
		}
	}

	async getByProductId(productId: string): Promise<Subscription | null> {
		try {
			const query = `SELECT * FROM ${SUBSCRIPTION_TABLE} WHERE productId = ? ORDER BY createdAt DESC LIMIT 1`;
			const records = this.sql.exec<SubscriptionRecord>(query, [productId]).toArray();
			return records.length > 0
				? { ...records[0], isAutoRenewalEnabled: Boolean(records[0].isAutoRenewalEnabled) }
				: null;
		} catch (error) {
			console.error('Error running Subscription getByProductId', error);
			return null;
		}
	}
	async getRenewalHistoryItem(productId: string): Promise<Subscription | null> {
		try {
			const query = `
				SELECT *
				FROM subscription_history
				WHERE productId = ? AND eventType = 'RENEWAL'
				ORDER BY createdAt DESC
				LIMIT 1
			`;
			const records = this.sql.exec<SubscriptionRecord>(query, [productId]).toArray();

			if (records.length > 0) {
				return {
					...records[0],
					isAutoRenewalEnabled: Boolean(records[0].isAutoRenewalEnabled),
				};
			}

			return null;
		} catch (error) {
			console.error('Error running getRenewalHistoryItem', error);
			return null;
		}
	}

	async getAll(): Promise<Subscription[]> {
		try {
			const query = `SELECT * FROM ${SUBSCRIPTION_TABLE}`;
			const records = this.sql.exec<SubscriptionRecord>(query).toArray();
			return records.map(record => ({
				...records[0],
				isAutoRenewalEnabled: Boolean(records[0].isAutoRenewalEnabled),
			}));
		} catch (error) {
			console.error('Error running Subscription getAll', error);
			return [];
		}
	}

	async delete(subscriptionId: string): Promise<void> {
		const query = `DELETE FROM ${SUBSCRIPTION_TABLE} WHERE subscriptionId = ?`;
		this.sql.exec(query, [subscriptionId]);
	}

	async update(subscriptionId: string, updateData: Partial<Subscription>): Promise<Subscription> {
		try {
			const fieldsToUpdate = [];
			const valuesToUpdate = [];
			const currentDate = new Date().toISOString();

			if (updateData.status) {
				fieldsToUpdate.push('status = ?');
				valuesToUpdate.push(updateData.status);
			}

			if (updateData.isAutoRenewalEnabled != null) {
				fieldsToUpdate.push('isAutoRenewalEnabled = ?');
				valuesToUpdate.push(updateData.isAutoRenewalEnabled ? 1 : 0);
			}

			if (updateData.expiresAt) {
				fieldsToUpdate.push('expiresAt = ?');
				valuesToUpdate.push(updateData.expiresAt);
			}

			if (updateData.startedAt) {
				fieldsToUpdate.push('startedAt = ?');
				valuesToUpdate.push(updateData.startedAt);
			}

			fieldsToUpdate.push('updatedAt = ?');
			valuesToUpdate.push(currentDate);

			if (fieldsToUpdate.length === 1) {
				throw new Error('No fields to update');
			}

			const query = `UPDATE subscription SET ${fieldsToUpdate.join(
				', '
			)} WHERE subscriptionId = ? RETURNING *`;
			valuesToUpdate.push(subscriptionId);

			const result = this.sql.exec<SubscriptionRecord>(query, ...valuesToUpdate).toArray();
			if (result.length === 0) {
				throw new Error(`No rows returned after UPDATE operation for ${subscriptionId}`);
			}

			return {
				...result[0],
				isAutoRenewalEnabled: Boolean(result[0].isAutoRenewalEnabled),
			};
		} catch (error) {
			console.error('Error running Subscription update', error);
			throw error;
		}
	}
}
