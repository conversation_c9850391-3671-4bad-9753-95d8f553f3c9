import { updateUserSubscription } from '../app/apis';
import { SCHEMA_MIGRATION_TABLE } from '../constants';
import { Environment, ScheduledOperationCreateType } from '../types';
import { WithdrawalLimit } from '../types/withdrawals';
import {
	generateHash,
	getDateAfter45Days,
	getDateAfter90Days,
	getNextMonthStart,
	isSameMonthAndYear,
} from '../utils';
import { GroupContextRepository } from './GroupContextRepository';
import { KVRepository } from './KVRepository';
import { ScheduledOperationsRepository } from './ScheduledOperationRepository';
import { UserDataStoreManager } from './UserDataStoreManager';

const DURABLE_OBJECT_NAME_KEY = 'durable_object_name';

export class MigrationManager {
	private sql: SqlStorage;
	private env: Environment;
	private kvRepo: KVRepository;
	private groupContextRepo: GroupContextRepository;
	private scheduledOperationRepo: ScheduledOperationsRepository;

	constructor(
		sql: SqlStorage,
		env: Environment,
		kvRepo: KVRepository,
		groupContextRepo: GroupContextRepository,
		scheduledOperationRepo: ScheduledOperationsRepository
	) {
		this.sql = sql;
		this.env = env;
		this.kvRepo = kvRepo;
		this.groupContextRepo = groupContextRepo;
		this.scheduledOperationRepo = scheduledOperationRepo;
	}

	async migrate() {
		try {
			const result = this.sql
				.exec(`SELECT version FROM ${SCHEMA_MIGRATION_TABLE} ORDER BY version DESC LIMIT 1`)
				.toArray();
			let currentVersion = (result?.[0]?.version as number) || 1;
			const targetVersion = this.getLatestSchemaVersion();

			if (currentVersion == targetVersion) {
				return;
			}

			const userId = await this.kvRepo.get(DURABLE_OBJECT_NAME_KEY);
			if (userId == null) {
				console.log(
					'Skipping migrations because userId is not set. This will likely run after the durable object is instantiated or a next request shows up.'
				);
				return;
			}

			for (let version = currentVersion + 1; version <= targetVersion; version++) {
				await this.runMigration(version, userId);
			}
		} catch (error) {
			console.error('Migration check failed:', error);
			throw new Error('Migration check failed');
		}
	}

	private async fixGroupContextScheduledOperationsMigration() {
		console.log('Starting fix for incorrect scheduled operations');

		const scheduledOperations = await this.scheduledOperationRepo.getAllByType(
			'group-context-expiry'
		);

		for (const operation of scheduledOperations) {
			try {
				const data = operation.data as any;
				if (data.contextData && !data.groupContextId) {
					data.groupContextId = operation.domainId;
					delete data.contextData;
					await this.scheduledOperationRepo.update(operation.id, {
						data: JSON.stringify(data),
					});
					console.log(`Fixed scheduled operation with ID: ${operation.id}`);
				}
			} catch (error) {
				console.error(`Error fixing scheduled operation with ID: ${operation.id}`, error);
			}
		}

		console.log('Completed fix for incorrect scheduled operations');
	}

	private async runMigration(version: number, userId: string) {
		try {
			const id = this.env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName(userId);
			const stub = this.env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
			switch (version) {
				case 2:
					console.log('Applying migration for version 2...');
					await this.createScheduledOperationRecordsForPendingGroupContextsMigration();
					await this.createWithdrawalLimitMigration();
					// Update the schema version after successful migration
					this.sql.exec(`INSERT INTO ${SCHEMA_MIGRATION_TABLE} (version) VALUES (${version})`);
					console.log(`Migration to version ${version} completed successfully`);
					break;

				case 3:
					console.log('Applying migration for version 3...');
					await this.fixGroupContextScheduledOperationsMigration();
					// Update the schema version after successful migration
					this.sql.exec(`INSERT INTO ${SCHEMA_MIGRATION_TABLE} (version) VALUES (${version})`);
					console.log(`Migration to version ${version} completed successfully`);
					break;
				case 4:
					console.log('Applying migration for version 4...');

					await this.fixOldWithdrawalLimitData(stub);
					await this.createScheduledOperationForWithdrawalLimitData(stub, userId);
					// Update the schema version after successful migration
					this.sql.exec(`INSERT INTO ${SCHEMA_MIGRATION_TABLE} (version) VALUES (${version})`);
					console.log(`Migration to version ${version} completed successfully`);
					break;
				case 5:
					console.log('Applying migration for version 5...');
					await this.createScheduledOperationForDataCleanup(stub, userId);
					// Update the schema version after successful migration
					this.sql.exec(`INSERT INTO ${SCHEMA_MIGRATION_TABLE} (version) VALUES (${version})`);
					console.log(`Migration to version ${version} completed successfully`);
					break;
				case 6:
					console.log('Applying migration for version 6...');
					await this.createScheduledOperationForTaskOpportunityReferenceCleanup(stub, userId);
					// Update the schema version after successful migration
					this.sql.exec(`INSERT INTO ${SCHEMA_MIGRATION_TABLE} (version) VALUES (${version})`);
					console.log(`Migration to version ${version} completed successfully`);
					break;
				case 7:
					console.log('Applying migration for version 7...');

					// Fetch the latest subscription history entry with eventType = 'RENEWAL'
					const latestRenewal = await stub.getRenewalHistoryItem(
						this.env.QUICKBUCKS_PRO_PRODUCT_ID
					);

					if (latestRenewal == null) {
						console.log('No renewal found. Skipping migration.', { userId });
						// Update the schema version after successful migration
						this.sql.exec(`INSERT INTO ${SCHEMA_MIGRATION_TABLE} (version) VALUES (${version})`);
						console.log(`Migration to version ${version} completed successfully`);
						break;
					}
					// Check if the corresponding subscription exists
					const subscription = await stub.getSubscriptionById(latestRenewal.subscriptionId);

					if (subscription == null) {
						console.log(
							`Subscription with ID ${latestRenewal.subscriptionId} not found. It might have expired. Skipping migration for ${userId}.`
						);
						// Update the schema version after successful migration
						this.sql.exec(`INSERT INTO ${SCHEMA_MIGRATION_TABLE} (version) VALUES (${version})`);
						console.log(`Migration to version ${version} completed successfully`);
						break;
					}

					// Update subscription's startedAt to match renewal createdAt
					const updatedSubscription = await stub.updateSubscription(subscription.subscriptionId, {
						startedAt: latestRenewal.createdAt,
					});
					const idempotencyKey = await generateHash(
						`${userId}-${subscription.subscriptionId}-migrateStartedAt`
					);
					await updateUserSubscription(
						this.env,
						userId,
						updatedSubscription.productId,
						idempotencyKey,
						updatedSubscription
					);

					console.log(
						`Updated subscription ${updatedSubscription.subscriptionId} startedAt from ${subscription.startedAt} to ${latestRenewal.createdAt} for ${userId}.`
					);

					// Update the schema version after successful migration
					this.sql.exec(`INSERT INTO ${SCHEMA_MIGRATION_TABLE} (version) VALUES (${version})`);
					console.log(`Migration to version ${version} completed successfully`);
					break;
				case 8:
					console.log('Applying migration for version 8...');
					await stub.scheduleNextWeeklyEngagementEmail();
					// Update the schema version after successful migration
					this.sql.exec(`INSERT INTO ${SCHEMA_MIGRATION_TABLE} (version) VALUES (${version})`);
					console.log(`Migration to version ${version} completed successfully`);
					break;
				case 9:
					console.log('Applying migration for version 9...');
					await this.addAccumulatedWithdrawalAmountMigration();

					// Update the schema version after successful migration
					this.sql.exec(`INSERT INTO ${SCHEMA_MIGRATION_TABLE} (version) VALUES (${version})`);
					console.log(`Migration to version ${version} completed successfully`);
					break;
				default:
					throw new Error(`Unknown migration version: ${version}`);
			}
		} catch (error) {
			console.error(`Migration to version ${version} failed:`, error);
			throw new Error(`Migration to version ${version} failed`);
		}
	}

	private async createScheduledOperationForTaskOpportunityReferenceCleanup(
		stub: DurableObjectStub<UserDataStoreManager>,
		userId: string
	) {
		const nextCleanupDate = getDateAfter45Days();
		await stub.createScheduledOperationAndScheduleNextAlarm({
			domainId: userId,
			runAt: nextCleanupDate,
			status: 'pending',
			type: 'clean-up-task-opportunity-reference',
		});
		console.log(`clean-up-task-opportunity-reference scheduled for ${nextCleanupDate}`);
	}

	private async createScheduledOperationForDataCleanup(
		stub: DurableObjectStub<UserDataStoreManager>,
		userId: string
	) {
		const nextCleanupDate = getDateAfter90Days();
		await stub.createScheduledOperationAndScheduleNextAlarm({
			domainId: userId,
			runAt: nextCleanupDate,
			status: 'pending',
			type: 'clean-up-completed-scheduled-operations',
			data: JSON.stringify({ userId }),
		});
		console.log(`clean-up-completed-scheduled-operations scheduled for ${nextCleanupDate}`);
	}

	private async createScheduledOperationForWithdrawalLimitData(
		stub: DurableObjectStub<UserDataStoreManager>,
		userId: string
	) {
		const nextRenewalTime = getNextMonthStart();

		await stub.createScheduledOperationAndScheduleNextAlarm({
			domainId: userId,
			runAt: nextRenewalTime,
			status: 'pending',
			type: 'reset-withdrawal-data',
			data: JSON.stringify({ userId }),
		});
		console.log(`reset-withdrawal-data scheduled for ${nextRenewalTime}`);
	}

	private async fixOldWithdrawalLimitData(stub: DurableObjectStub<UserDataStoreManager>) {
		let withdrawalLimit = await stub.getWithdrawalLimit();
		if (withdrawalLimit.success) {
			const now = new Date();
			const nowMillis = now.getTime();
			const lastResetDate = new Date(withdrawalLimit.data.lastResetDate || 0);
			if (isSameMonthAndYear(lastResetDate, now)) {
				console.log('Withdrawal limit is already up-to-date');
				return;
			}
			//@ts-ignore accumulatedWithdrawalAmount attribute was added on 10/04/25, didn't exist on this migration
			const resetWithdrawalLimit: WithdrawalLimit = {
				smallWithdrawalCount: 0,
				largeWithdrawalCount: 0,
				lastResetDate: nowMillis,
			};
			const resetLimitResponse = await stub.updateWithdrawalLimit(resetWithdrawalLimit);
			if (!resetLimitResponse.success) {
				throw Error(`Failed updating withdrawal limit ${resetLimitResponse.errorMessage}`);
			}
			console.log('Withdrawal limit reset successfully');
		} else {
			throw Error(`Migration v4 failed: withdrawalLimit data is null`);
		}
	}

	private async createWithdrawalLimitMigration() {
		// Set resetDate to today's date
		const resetDate = new Date().getTime();

		// Initialize default withdrawal data
		//@ts-ignore accumulatedWithdrawalAmount attribute was added on 10/04/25, didn't exist on this migration
		await this.kvRepo.put<WithdrawalLimit>('withdrawal_limit', {
			largeWithdrawalCount: 0,
			smallWithdrawalCount: 0,
			lastResetDate: resetDate,
		});

		console.log('Completed initializing default withdrawal data');
	}

	private async addAccumulatedWithdrawalAmountMigration() {
		// Adds accumulatedWithdrawalAmount attribute on WithdrawalLimit to help track the amount unverified users have withdrawn in each month
		const existingWithdrawalLimit = await this.kvRepo.get<WithdrawalLimit>('withdrawal_limit');

		if (existingWithdrawalLimit == null) {
			throw Error('Withdrawal limit is not initialized. This should never be the case.');
		}

		await this.kvRepo.put<WithdrawalLimit>('withdrawal_limit', {
			...existingWithdrawalLimit,
			accumulatedWithdrawalAmount: 0,
		});

		console.log(
			'Completed adding accumulatedWithdrawalAmount attribute on the existing withdrawal data'
		);
	}

	private async createScheduledOperationRecordsForPendingGroupContextsMigration() {
		const activeGroupContexts = await this.groupContextRepo.getAllActiveGroupContexts();

		for (const context of activeGroupContexts) {
			try {
				const runAt = new Date(context.createdDate);
				runAt.setDate(runAt.getDate() + this.env.GROUP_CONTEXT_EXPIRY_DAYS);

				const scheduledOperationData: ScheduledOperationCreateType = {
					type: 'group-context-expiry',
					status: 'pending',
					runAt: runAt.toISOString(),
					domainId: context.id,
					data: JSON.stringify({ groupId: context.groupId, groupContextId: context.id }),
				};

				const operation = await this.scheduledOperationRepo.create(scheduledOperationData);

				if (!operation) {
					console.warn(`Failed to create scheduled operation for groupContext: ${context.groupId}`);
				}
			} catch (error) {
				console.error(
					`Error creating scheduled operation for groupContext: ${context.groupId}`,
					error
				);
			}
		}
	}

	private getLatestSchemaVersion(): number {
		return 9;
	}
}
