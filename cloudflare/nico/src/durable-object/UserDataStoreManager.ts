import { DurableObject } from 'cloudflare:workers';
import {
	cleanupTaskOpportunityReference,
	deleteUserSubscription,
	forceDeleteUser,
	getUserInfo,
	processPayment,
	updateUserSubscription,
} from '../app/apis';
import { AssignmentAccumulatorHelper } from '../app/assignment-accumulator';
import {
	CREATE_ASSIGNMENT_TABLE_QUERY,
	CREATE_GROUP_CONTEXT_TABLE_QUERY,
	CREATE_KV_STORAGE_TABLE_QUERY,
	CREATE_MIGRATIONS_TABLE_QUERY,
	CREATE_REWARDS_TABLE_QUERY,
} from '../constants';
import { CREATE_SCHEDULED_OPERATIONS_TABLE_QUERY } from '../constants/db/scheduled-operations';
import {
	CREATE_SUBSCRIPTION_HISTORY_TABLE_QUERY,
	CREATE_SUBSCRIPTION_TABLE_QUERY,
} from '../constants/db/subscription';
import {
	AccumulatedAssignmentGroupDataStatsType,
	AccumulatedAssignmentGroupDataType,
	AccumulatorGroupContextDataSchema,
	AssignmentCreateType,
	EmailActivityMessage,
	Environment,
	GroupContextCreateType,
	GroupContextPatchType,
	GroupContextSchedulerDataSchema,
	GroupContextType,
	RewardCreateType,
	RewardResponseType,
	RPCResponse,
	ScheduledOperation,
	ScheduledOperationCreateType,
	ScheduledOperationStatus,
	Subscription,
	SubscriptionCreate,
	SubscriptionHistory,
	SubscriptionHistoryCreate,
	SubscriptionRenewalSchedulerDataSchema,
	User,
	UserDeletionDataSchema,
} from '../types';
import { AllowedWithdrawalCount, PayoutLimits, WithdrawalLimit } from '../types/withdrawals';
import {
	calculateExpirationDate,
	calculateSubscriptionCost,
	ErrorResponseType,
	errorRPCResponse,
	generateHash,
	getDateAfter45Days,
	getDateAfter90Days,
	getDateAfterAWeek,
	getNextMonthStart,
	hasActiveSubscription,
	mapToGroupDataType,
	getAllowedWithdrawalCount,
	successRPCResponse,
	toSubscriptionHistory,
} from '../utils';
import { AssignmentRepository } from './AssignmentRepository';
import { GroupContextRepository } from './GroupContextRepository';
import { KVRepository } from './KVRepository';
import { MigrationManager } from './MigrationManager';
import { RewardRepository } from './RewardRepository';
import { ScheduledOperationsRepository } from './ScheduledOperationRepository';
import { SubscriptionHistoryRepository } from './SubscriptionHistoryRepository';
import { SubscriptionRepository } from './SubscriptionRepository';

const DURABLE_OBJECT_NAME_KEY = 'durable_object_name';
const WITHDRAWAL_LIMIT_KEY = 'withdrawal_limit';

export class UserDataStoreManager extends DurableObject<Environment> {
	groupContextRepo: GroupContextRepository;
	assignmentRepo: AssignmentRepository;
	rewardRepo: RewardRepository;
	kvRepo: KVRepository;
	scheduledOperationsRepo: ScheduledOperationsRepository;
	subscriptionRepo: SubscriptionRepository;
	subscriptionHistoryRepo: SubscriptionHistoryRepository;

	constructor(ctx: DurableObjectState, env: Environment) {
		super(ctx, env);

		this.initializeTables(ctx.storage.sql);
		this.groupContextRepo = new GroupContextRepository(ctx.storage.sql);
		this.assignmentRepo = new AssignmentRepository(ctx.storage.sql);
		this.rewardRepo = new RewardRepository(ctx.storage.sql);
		this.kvRepo = new KVRepository(ctx.storage.sql);
		this.subscriptionRepo = new SubscriptionRepository(ctx.storage.sql);
		this.subscriptionHistoryRepo = new SubscriptionHistoryRepository(ctx.storage.sql);
		this.scheduledOperationsRepo = new ScheduledOperationsRepository(ctx.storage.sql);
		this.runMigrations();
	}

	initializeTables(sql: SqlStorage) {
		sql.exec(CREATE_MIGRATIONS_TABLE_QUERY);
		sql.exec(CREATE_GROUP_CONTEXT_TABLE_QUERY);
		sql.exec(CREATE_ASSIGNMENT_TABLE_QUERY);
		sql.exec(CREATE_REWARDS_TABLE_QUERY);
		sql.exec(CREATE_KV_STORAGE_TABLE_QUERY);
		sql.exec(CREATE_SCHEDULED_OPERATIONS_TABLE_QUERY);
		sql.exec(CREATE_SUBSCRIPTION_TABLE_QUERY);
		sql.exec(CREATE_SUBSCRIPTION_HISTORY_TABLE_QUERY);
	}

	async runMigrations() {
		await new MigrationManager(
			this.ctx.storage.sql,
			this.env,
			this.kvRepo,
			this.groupContextRepo,
			this.scheduledOperationsRepo
		).migrate();
	}

	async storeDurableObjectName(name: string) {
		if ((await this.kvRepo.get(DURABLE_OBJECT_NAME_KEY)) == null) {
			await this.kvRepo.put(DURABLE_OBJECT_NAME_KEY, name);
		}
	}

	async getSubscriptionById(subscriptionId: string): Promise<Subscription | null> {
		return this.subscriptionRepo.getById(subscriptionId);
	}

	async getAllUserSubscriptions(): Promise<Subscription[]> {
		return this.subscriptionRepo.getAll();
	}

	async getSubscriptionByProductId(productId: string): Promise<Subscription | null> {
		return this.subscriptionRepo.getByProductId(productId);
	}

	async getRenewalHistoryItem(productId: string): Promise<Subscription | null> {
		return this.subscriptionRepo.getRenewalHistoryItem(productId);
	}

	async updateSubscription(subscriptionId: string, subscription: Partial<Subscription>) {
		return this.subscriptionRepo.update(subscriptionId, subscription);
	}

	async deleteSubscription(subscription: Subscription) {
		return this.subscriptionRepo.delete(subscription.subscriptionId);
	}

	async createSubscription(subscription: SubscriptionCreate) {
		const createdSubscription = await this.subscriptionRepo.create(subscription);
		await this.saveSubscriptionHistory(
			toSubscriptionHistory(createdSubscription, {
				previousStatus: null,
				currentStatus: 'ACTIVE',
				eventType: 'ACTIVATION',
			})
		);
		return createdSubscription;
	}

	async saveSubscriptionHistory(historyItem: SubscriptionHistoryCreate) {
		return this.subscriptionHistoryRepo.create(historyItem);
	}

	async getDurableObjectName() {
		return this.kvRepo.get(DURABLE_OBJECT_NAME_KEY);
	}

	async getWithdrawalLimit(): Promise<RPCResponse<WithdrawalLimit>> {
		const withdrawalLimit = await this.kvRepo.get<WithdrawalLimit>(WITHDRAWAL_LIMIT_KEY);
		if (withdrawalLimit) {
			return successRPCResponse(withdrawalLimit);
		}
		return errorRPCResponse('WITHDRAWAL_LIMIT_NOT_FOUND', 404);
	}

	async getUserPayoutLimit(): Promise<PayoutLimits> {
		const withdrawalLimit = await this.kvRepo.get<WithdrawalLimit>(WITHDRAWAL_LIMIT_KEY);
		if (withdrawalLimit == null) {
			throw Error('Withdrawal limit is not initialized. This should never be the case.');
		}
		const userSubscription = await this.subscriptionRepo.getByProductId(
			this.env.QUICKBUCKS_PRO_PRODUCT_ID
		);

		const isSubscribed = hasActiveSubscription(userSubscription);

		const { totalWithdrawalCount }: AllowedWithdrawalCount = getAllowedWithdrawalCount(
			this.env,
			isSubscribed
		);

		return {
			largePayoutsUsed: withdrawalLimit.largeWithdrawalCount,
			smallPayoutsUsed: withdrawalLimit.smallWithdrawalCount,
			totalPayoutsLimit: totalWithdrawalCount,
			withdrawalProgress: {
				currentWithdrawalAmount: withdrawalLimit.accumulatedWithdrawalAmount,
				maximumWithdrawalAmount: this.env.WITHDRAWAL_LIMIT_NOTSUBSCRIBED_USD_AMOUNT
			}
		};
	}

	async updateWithdrawalLimit(data: WithdrawalLimit): Promise<RPCResponse<WithdrawalLimit>> {
		const withdrawalLimit = await this.kvRepo.put<WithdrawalLimit>(WITHDRAWAL_LIMIT_KEY, data);
		if (withdrawalLimit) {
			return successRPCResponse(withdrawalLimit);
		}
		return errorRPCResponse('INTERNAL_ERROR', 500);
	}

	async getGroupContextById(contextId: string): Promise<RPCResponse<GroupContextType>> {
		const result = await this.groupContextRepo.getById(contextId);
		if (result.length > 0) {
			return successRPCResponse(mapToGroupDataType(result[0]));
		} else {
			return errorRPCResponse('GROUP_CONTEXT_NOT_FOUND', 404);
		}
	}

	async deleteGroupContextById(contextId: string): Promise<RPCResponse<{ message: string }>> {
		const result = await this.groupContextRepo.deleteById(contextId);
		return successRPCResponse({ message: 'SUCCESSFULLY_DELETED' }, 204);
	}

	async getAllGroupContextsByGroupId(groupId: string): Promise<RPCResponse<GroupContextType[]>> {
		const result = await this.groupContextRepo.getAllByGroupId(groupId);
		return successRPCResponse(result.map(mapToGroupDataType));
	}

	async getAllGroupContexts(): Promise<RPCResponse<GroupContextType[]>> {
		const result = await this.groupContextRepo.getAll();
		return successRPCResponse(result.map(mapToGroupDataType));
	}

	async createGroupContext(
		groupContextData: GroupContextCreateType
	): Promise<RPCResponse<GroupContextType>> {
		try {
			const result = await this.groupContextRepo.create(groupContextData);
			return successRPCResponse(mapToGroupDataType(result[0]));
		} catch (error) {
			console.error('Failed to create group context', error);
			return errorRPCResponse('FAILED_CREATE_GROUP_CONTEXT', 500);
		}
	}

	async patchGroupContext(
		groupContextId: string,
		patchData: Partial<GroupContextCreateType>
	): Promise<RPCResponse<GroupContextType>> {
		try {
			const result = await this.groupContextRepo.update(groupContextId, patchData);
			return successRPCResponse(mapToGroupDataType(result[0]));
		} catch (error) {
			console.error('Failed to update group context', error);
			// Requires to throw error the storage transaction rollback works only if the subsequent method throws
			throw Error(`Failed to update group context ${error}`);
		}
	}

	async getLatestGroupContext(
		groupId: string,
		enableStats: boolean = false
	): Promise<RPCResponse<GroupContextType | AccumulatedAssignmentGroupDataStatsType>> {
		const result = await this.groupContextRepo.getLatestByGroupId(groupId);
		if (result.length > 0) {
			if (enableStats) {
				const groupData = mapToGroupDataType(result[0]);
				const latestGroupData = AccumulatorGroupContextDataSchema.parse(groupData);
				const accumulationStats = AssignmentAccumulatorHelper.getAccumulationStats(
					latestGroupData.type,
					latestGroupData.contextData
				);
				return successRPCResponse({ ...groupData, contextData: accumulationStats });
			}
			return successRPCResponse(mapToGroupDataType(result[0]));
		} else {
			return errorRPCResponse('GROUP_CONTEXT_NOT_FOUND', 404);
		}
	}

	async patchLatestGroupContext(
		groupId: string,
		patchData: GroupContextPatchType
	): Promise<RPCResponse<GroupContextType>> {
		try {
			const latestGroupContext = await this.getLatestGroupContext(groupId);
			if (!latestGroupContext.success) {
				return errorRPCResponse(latestGroupContext.errorCode, latestGroupContext.statusCode);
			}

			const { status, id } = latestGroupContext.data;

			if (patchData.rewardId) {
				if (
					latestGroupContext.data.rewardId != null &&
					patchData.rewardId !== latestGroupContext.data.rewardId
				) {
					console.error(
						'Group context rewardId cannot be overridden. Found',
						`Existing:${latestGroupContext.data.rewardId}`,
						`New:${patchData.rewardId}`
					);
					return errorRPCResponse('CANNOT_UPDATE_REWARD_ID', 400);
				}
			}

			if (patchData.status === 'completed') {
				if (status !== 'pending-payment') {
					return errorRPCResponse('CANNOT_MARK_AS_COMPLETED', 400, 'en', {
						status,
					});
				}
			} else if (patchData.status === 'expired') {
				if (status !== 'active') {
					return errorRPCResponse('CANNOT_MARK_AS_COMPLETED', 400, 'en', {
						status,
					});
				}
			} else if (patchData.status != null) {
				return errorRPCResponse('INVALID_STATUS_UPDATE', 400);
			}

			const updatedContext = await this.groupContextRepo.update(id, patchData);

			return successRPCResponse(mapToGroupDataType(updatedContext[0]));
		} catch (error) {
			console.error('Failed to patch latest group context', error);
			return errorRPCResponse('FAILED_PATCH_GROUP_CONTEXT', 500);
		}
	}

	async getScheduledAlarm() {
		const alarmTime = await this.ctx.storage.getAlarm();
		const date = alarmTime ? new Date(alarmTime) : null;
		return successRPCResponse({ scheduledAt: date });
	}

	async getScheduledOperations(status?: ScheduledOperationStatus) {
		return await this.scheduledOperationsRepo.getAll(status);
	}

	async getSubscriptionHistoryBySubscriptionId(
		subscriptionId: string
	): Promise<SubscriptionHistory[]> {
		return await this.subscriptionHistoryRepo.getBySubscriptionId(subscriptionId);
	}

	async getSubscriptionHistoryByProductId(subscriptionId: string): Promise<SubscriptionHistory[]> {
		return await this.subscriptionHistoryRepo.getByProductId(subscriptionId);
	}

	async createScheduledOperationAndScheduleNextAlarm(
		scheduledOperationCreate: ScheduledOperationCreateType
	) {
		await this.scheduledOperationsRepo.create(scheduledOperationCreate);
		this.scheduleNextAlarm();
	}

	async scheduleNextWeeklyEngagementEmail() {
		const userId = await this.getDurableObjectName();
		if (!userId)
			throw Error('Error while scheduleNextWeeklyEngagementEmail. durable object name not set yet');
		const runAt = getDateAfterAWeek();
		const scheduleWeeklyEmailOperation: ScheduledOperationCreateType = {
			domainId: userId,
			runAt: runAt,
			status: 'pending',
			type: 'send-weekly-engagement-email',
		};
		await this.scheduledOperationsRepo.create(scheduleWeeklyEmailOperation);
	}

	async createOrUpdateScheduledOperationForUserDeletion(
		scheduledOperationCreate: ScheduledOperationCreateType
	) {
		const pendingUserDeletionOperation =
			await this.scheduledOperationsRepo.getLastPendingOperationByType('user-hard-delete');
		if (pendingUserDeletionOperation) {
			await this.scheduledOperationsRepo.update(pendingUserDeletionOperation.id, {
				runAt: scheduledOperationCreate.runAt,
			});
			this.scheduleNextAlarm();
			return;
		}
		await this.createScheduledOperationAndScheduleNextAlarm(scheduledOperationCreate);
	}

	private async handleUserDeletion(userId: string, operation: ScheduledOperation) {
		const operationData = UserDeletionDataSchema.parse(operation.data);
		try {
			const userResponse = await getUserInfo(this.env, operationData.userId);
			if (!userResponse.ok) {
				throw Error(`Error getting user data ${await userResponse.text()}`);
			}
			const user = (await userResponse.json()) as User;
			if (!user.isDeleted) {
				console.info(
					'User is not marked for deletion. User might have reactivated their account. Skipping hard delete',
					{ userId }
				);
				return;
			}

			const response = await forceDeleteUser(this.env, operationData.userId);
			if (!response.ok) {
				const error = await response.text();
				throw Error(`Failed to hard delete user ${error}`);
			}
		} catch (error) {
			console.error('Error while running user deletion operation', { userId, operation });
			throw error;
		}
	}

	async handleResetWithdrawalData(userId: string, operation: ScheduledOperation) {
		try {
			// Reset withdrawal limits
			await this.resetWithdrawalLimits();

			// Schedule the next renewal operation
			await this.scheduleNextWithdrawalReset(userId);
		} catch (error) {
			console.error(`Failed to handle reset withdrawal data with `, { error, userId });
			throw Error(`Failed to handle reset withdrawal data: ${{ error, userId }}`);
		}
	}

	private async resetWithdrawalLimits() {
		const nowMillis = Date.now();
		const resetWithdrawalLimit: WithdrawalLimit = {
			smallWithdrawalCount: 0,
			largeWithdrawalCount: 0,
			lastResetDate: nowMillis,
			accumulatedWithdrawalAmount: 0
		};

		const resetLimitResponse = await this.updateWithdrawalLimit(resetWithdrawalLimit);
		if (!resetLimitResponse.success) {
			throw Error(`Failed to update withdrawal limits: ${resetLimitResponse.errorMessage}`);
		}
	}

	private async scheduleNextWithdrawalReset(userId: string) {
		const nextRenewalTime = getNextMonthStart();
		await this.scheduledOperationsRepo.create({
			type: 'reset-withdrawal-data',
			status: 'pending',
			domainId: userId,
			runAt: nextRenewalTime,
			data: JSON.stringify({ userId }),
		});
	}

	async handleD1Cleanup(userId: string, operation: ScheduledOperation): Promise<void> {
		try {
			// cleaning up scheduledOperation table
			await this.scheduledOperationsRepo.cleanupCompletedOperationsBefore90Days();
			// add code to clean up more tables etc..
			await this.scheduleNextCleanup(operation.domainId);
		} catch (error) {
			const errorMessage = `Failed to handle cleanup d1`;
			console.error(errorMessage, { error, userId });
			throw Error(`${errorMessage}, ${{ error, userId }}`);
		}
	}

	private async scheduleNextCleanup(userId: string) {
		const nextCleanupDate = getDateAfter90Days();
		await this.scheduledOperationsRepo.create({
			type: 'clean-up-completed-scheduled-operations',
			status: 'pending',
			domainId: userId,
			runAt: nextCleanupDate,
			data: JSON.stringify({ userId }),
		});
	}

	async handleSendWeeklyEngagementEmail(
		userId: string,
		operation: ScheduledOperation
	): Promise<void> {
		try {
			const emailActivityMessage: EmailActivityMessage = {
				userId,
				templateName: 'WEEKLY_EARNINGS',
			};
			console.log(`sending message to the email activity queue `, emailActivityMessage);
			await this.env.EMAIL_ACTIVITY_QUEUE.send(emailActivityMessage);
			// schedule another weekly engagement email
			await this.scheduleNextWeeklyEngagementEmail();
		} catch (error) {
			const errorMessage = `Failed to sendWeeklyEngagementEmail ${operation}`;
			console.error(errorMessage, { error, userId });
			throw Error(`${errorMessage}, ${{ error, userId }}`);
		}
	}

	async handleTaskOpportunityReferenceCleanup(
		userId: string,
		operation: ScheduledOperation
	): Promise<void> {
		try {
			const idempotencyKey = await generateHash(`${userId}:${operation.id}`);
			await cleanupTaskOpportunityReference(this.env, userId, idempotencyKey);
			await this.scheduleNextTaskOpportunityReferenceCleanup(userId);
		} catch (error) {
			const errorMessage = `Failed to handle cleanUpTaskOpportunityReference`;
			console.error(errorMessage, { error: JSON.stringify(error), userId });
			throw Error(`${errorMessage}, ${{ error, userId }}`);
		}
	}

	private async scheduleNextTaskOpportunityReferenceCleanup(userId: string) {
		const nextCleanupDate = getDateAfter45Days();
		await this.scheduledOperationsRepo.create({
			type: 'clean-up-task-opportunity-reference',
			status: 'pending',
			domainId: userId,
			runAt: nextCleanupDate,
		});
	}

	async handleCreateGroupContext(groupId: string, groupCreate: GroupContextCreateType) {
		let latestContext = await this.getLatestGroupContext(groupId);
		if (latestContext.success && latestContext.data) {
			return errorRPCResponse('ACTIVE_GROUP_CONTEXT_ALREADY_EXISTS', 400);
		}
		const groupContext = await this.createGroupContext({
			groupId,
			status: 'active',
			type: groupCreate.type,
			contextData: AssignmentAccumulatorHelper.prepareInitialMetadata(),
		});
		if (groupContext.success) {
			const EXPIRY_DAYS = this.env.GROUP_CONTEXT_EXPIRY_DAYS;
			this.createScheduledOperationAndScheduleNextAlarm({
				type: 'group-context-expiry',
				status: 'pending',
				domainId: groupContext.data.id,
				runAt: new Date(Date.now() + EXPIRY_DAYS * 24 * 60 * 60 * 1000).toISOString(),
				data: JSON.stringify({
					groupId,
					groupContextId: groupContext.data.id,
				}),
			});
		}
		return successRPCResponse(groupContext.data, 201);
	}

	async releaseTransaction(userId: string, groupData: { groupId: string; groupContextId: string }) {
		const response = await fetch(
			`${this.env.SUNNY_API_ENDPOINT}/v1/internal/webhooks/transactions/release`,
			{
				method: 'post',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Basic ${btoa(
						`${this.env.CLOUDFLARE_INTEGRATION_USER_USERNAME}:${this.env.CLOUDFLARE_INTEGRATION_USER_PASSWORD}`
					)}`,
				},
				body: JSON.stringify({ ...groupData, userId }),
			}
		);
		return response;
	}

	async scheduleNextAlarm() {
		const nextOperation = await this.scheduledOperationsRepo.getNextScheduledOperation();
		if (nextOperation) {
			const currentTime = Date.now();
			const nextAlarmScheduleTime = new Date(nextOperation.runAt).getTime();
			if (nextAlarmScheduleTime < currentTime) {
				await this.ctx.storage.setAlarm(currentTime);
			} else {
				await this.ctx.storage.setAlarm(nextAlarmScheduleTime);
			}
		}
	}

	async executeScheduledOperationWithId(scheduledOperationId: string) {
		const operation = await this.scheduledOperationsRepo.getById(scheduledOperationId);
		if (!operation) {
			throw Error(`No scheduled operation with ID ${scheduledOperationId}`);
		}
		if (operation.status === 'completed') {
			throw Error(`scheduled operation with ID ${scheduledOperationId} is already completed`);
		}
		await this.executeScheduledOperation(operation);
	}

	async executeScheduledOperation(operation: ScheduledOperation) {
		const userId = await this.getDurableObjectName();

		if (!userId) {
			console.error('Scheduled operation failed to run. UserId is null', operation);
			return;
		}

		await this.ctx.storage
			.transaction(async () => {
				switch (operation.type) {
					case 'group-context-expiry':
						await this.handleGroupContextExpiry(userId, operation);
						break;
					case 'renew-subscription':
						await this.handleSubscriptionRenewal(userId, operation);
						break;
					case 'user-hard-delete':
						await this.handleUserDeletion(userId, operation);
						break;
					case 'reset-withdrawal-data':
						await this.handleResetWithdrawalData(userId, operation);
						break;
					case 'clean-up-completed-scheduled-operations':
						await this.handleD1Cleanup(userId, operation);
						break;
					case 'send-weekly-engagement-email':
						await this.handleSendWeeklyEngagementEmail(userId, operation);
						break;
					case 'clean-up-task-opportunity-reference':
						await this.handleTaskOpportunityReferenceCleanup(userId, operation);
						break;
					default:
						console.warn('Unhandled operation type', { type: operation.type, userId });
				}
				await this.scheduledOperationsRepo.update(operation.id, { status: 'completed' });
			})
			.catch(error => {
				console.error(`Failed to execute scheduled operation`, {
					error,
					operationId: operation.id,
					userId,
				});
				throw error;
			});
	}

	async alarm(alarmInfo: AlarmInvocationInfo) {
		const alarmDate = Date.now();
		const operation = await this.scheduledOperationsRepo.getPendingOperation(alarmDate);

		if (operation) {
			if (alarmInfo?.isRetry) {
				if (alarmInfo.retryCount > 5) {
					console.warn(
						'Retry count exceeded 5, marking the operation as failed',
						alarmInfo.retryCount
					);
					await this.scheduledOperationsRepo.update(operation.id, { status: 'failed' });
					await this.scheduleNextAlarm();
					return;
				}
				console.warn('Retry operation', alarmInfo.retryCount);
			}
			await this.executeScheduledOperation(operation);
		} else {
			const userId = await this.getDurableObjectName();
			console.error('No scheduled operation was found that needs to be runAt', {
				alarmDate,
				userId,
			});
		}
		await this.scheduleNextAlarm();
	}

	private async handleGroupContextExpiry(userId: string, operation: ScheduledOperation) {
		try {
			const groupData = GroupContextSchedulerDataSchema.parse(operation.data);
			const groupContext = await this.groupContextRepo.getById(groupData.groupContextId);
			if (groupContext[0].status === 'completed' || groupContext[0].status === 'pending-payment') {
				console.log(`Ignoring group context expiration since status is ${groupContext[0].status}`, {
					operation,
					userId,
				});
				return;
			}

			const response = await this.releaseTransaction(userId, groupData);

			if (response.ok) {
				await Promise.all([
					this.groupContextRepo.update(groupData.groupContextId, { status: 'expired' }),
				]);
			} else {
				const error = await response.text();
				throw new Error(`Release transaction failed with error ${error}`);
			}
		} catch (error) {
			console.error(`Error handling group context expiration operation`, {
				error,
				userId,
				operation,
			});
			throw error;
		}
	}

	private async handleSubscriptionRenewal(userId: string, operation: ScheduledOperation) {
		try {
			const subscriptionRenewalData = SubscriptionRenewalSchedulerDataSchema.parse(operation.data);
			const { subscriptionId } = subscriptionRenewalData;

			const subscription = await this.getSubscriptionById(subscriptionId);
			if (!subscription) {
				throw new Error(
					`Renewal failed: No subscription available for ID: ${subscriptionId}, for user ${userId}`
				);
			}

			if (subscription.status === 'CANCELLED') {
				await this.expireSubscription(userId, subscription, operation.id);
			} else if (subscription.status === 'ACTIVE') {
				await this.renewActiveSubscription(userId, subscription, operation);
			}
		} catch (error) {
			console.error(`Error handling group context expiration operation`, {
				error,
				userId,
				operation,
			});
			throw error;
		}
	}

	private async expireSubscription(
		userId: string,
		subscription: Subscription,
		operationId: string
	) {
		await this.deleteSubscription(subscription);
		await this.saveSubscriptionHistory(
			toSubscriptionHistory(subscription, {
				currentStatus: 'EXPIRED',
				previousStatus: subscription.status,
				eventType: 'EXPIRATION',
			})
		);
		await deleteUserSubscription(this.env, userId, subscription.productId, operationId);
	}

	private async renewActiveSubscription(
		userId: string,
		subscription: Subscription,
		operation: ScheduledOperation
	) {
		const runAtDate = new Date(operation.runAt);
		const idempotencyKey = await generateHash(
			`${userId}-${subscription.subscriptionId}-${runAtDate.getTime()}`
		);

		const response = await processPayment(this.env, userId, idempotencyKey, {
			amount: calculateSubscriptionCost(subscription.productId, subscription.billingCycle),
			type: 'REWARD_POINTS',
			description: 'QuickBucks Pro Subscription Renewal',
		});

		if (response.ok) {
			await this.completeSubscriptionRenewal(userId, subscription, idempotencyKey, operation.id);
		} else if (response.status === 400) {
			const responseBody = (await response.json()) as ErrorResponseType;
			if (responseBody.detail[0].errorCode === 'INSUFFICIENT_PAYIN_BALANCE') {
				await this.expireSubscription(userId, subscription, operation.id);
				console.info(
					'Expiring the subscription since user does not have enough balance for renewal for',
					userId
				);
			} else {
				throw new Error(
					`Failed while processing payment with 400: ${JSON.stringify(responseBody)}`
				);
			}
		} else {
			const responseText = await response.text();
			throw new Error(`Failed while processing payment with status code ${responseText}`);
		}
	}

	async completeSubscriptionRenewal(
		userId: string,
		subscription: Subscription,
		idempotencyKey: string,
		operationId: string
	) {
		const nextRenewalTime = calculateExpirationDate(
			subscription.billingCycle,
			this.env.WORKER_ENVIRONMENT
		);

		const updatedSubscription = await this.updateSubscription(subscription.subscriptionId, {
			expiresAt: nextRenewalTime,
			startedAt: new Date().toISOString(),
		});

		await Promise.all([
			this.saveSubscriptionHistory(
				toSubscriptionHistory(updatedSubscription, {
					currentStatus: 'ACTIVE',
					previousStatus: 'ACTIVE',
					eventType: 'RENEWAL',
				})
			),
			updateUserSubscription(
				this.env,
				userId,
				subscription.productId,
				idempotencyKey,
				updatedSubscription
			),
			await this.scheduledOperationsRepo.create({
				type: 'renew-subscription',
				status: 'pending',
				domainId: subscription.subscriptionId,
				runAt: nextRenewalTime,
				data: JSON.stringify({
					subscriptionId: subscription.subscriptionId,
					billingCycle: subscription.billingCycle,
				}),
			}),
		]);

		console.info('Successfully handled alarm', { operationId, userId });
	}

	async handleAssignmentCompletion(
		groupId: string,
		assignmentData: AssignmentCreateType
	): Promise<RPCResponse<GroupContextType>> {
		return this.ctx.storage
			.transaction(async () => {
				const latestContext = await this.getLatestGroupContext(groupId);

				if (!latestContext.success) {
					return errorRPCResponse(latestContext.errorCode, latestContext.statusCode);
				}

				const latestGroupData = AccumulatorGroupContextDataSchema.parse(latestContext.data);
				const isAlreadyCompleted = AssignmentAccumulatorHelper.getAccumulationStats(
					latestGroupData.type,
					latestGroupData.contextData
				).isFulfilled;
				if (isAlreadyCompleted) {
					return errorRPCResponse('GROUP_ALREADY_COMPLETED', 400);
				}

				const assignment = await this.assignmentRepo.create(assignmentData, latestContext.data.id);

				const updatedContextData = AssignmentAccumulatorHelper.updateMetadata(
					latestGroupData.contextData,
					{
						assignmentId: assignment.assignmentId,
						action: 'add',
					}
				);

				const isGroupCompleted = AssignmentAccumulatorHelper.getAccumulationStats(
					latestContext.data.type,
					updatedContextData
				).isFulfilled;
				const updateResult = await this.patchGroupContext(latestContext.data!.id, {
					contextData: updatedContextData,
					...(isGroupCompleted && { status: 'pending-payment' }),
				});

				if (!updateResult.success) {
					return errorRPCResponse(updateResult.errorCode, updateResult.statusCode);
				}

				return successRPCResponse(updateResult.data);
			})
			.catch(error => {
				console.error('Failed to handle assignment completion', error);
				return errorRPCResponse('ERROR_HANDLING_ASSIGNMENT_COMPLETION', 500);
			});
	}

	async handleAssignmentRemove(assignmentId: string): Promise<RPCResponse<GroupContextType>> {
		try {
			const assignment = await this.assignmentRepo.getById(assignmentId);
			const latestContext = await this.getGroupContextById(assignment.groupContextId);

			if (!latestContext.success) {
				return errorRPCResponse(latestContext.errorCode, latestContext.statusCode);
			}

			if (latestContext.data?.status !== 'active') {
				return errorRPCResponse('GROUP_CONTEXT_NOT_ACTIVE', 400);
			}

			const updatedContextData = AssignmentAccumulatorHelper.updateMetadata(
				latestContext.data.contextData as AccumulatedAssignmentGroupDataType,
				{
					assignmentId,
					action: 'remove',
				}
			);

			const updateResult = await this.patchGroupContext(latestContext.data.id, {
				contextData: updatedContextData,
				status: 'active',
			});

			if (!updateResult.success) {
				return errorRPCResponse('ERROR_UPDATE_GROUP_CONTEXT', updateResult.statusCode);
			}

			return successRPCResponse(updateResult.data);
		} catch (error) {
			console.error(`Error when handling assignment remove`, error);
			return errorRPCResponse('ERROR_HANDLING_ASSIGNMENT_REMOVE', 500);
		}
	}

	async createReward(rewardData: RewardCreateType): Promise<RPCResponse<RewardResponseType>> {
		try {
			const data = await this.rewardRepo.create(rewardData);
			return successRPCResponse({ ...data, isComplete: data.currentProgress >= data.target }, 201);
		} catch (error) {
			console.error('Error while creating reward', rewardData, error);
			return errorRPCResponse('CANNOT_MARK_AS_COMPLETED');
		}
	}

	async getRewardById(id: string): Promise<RPCResponse<RewardResponseType>> {
		try {
			const reward = await this.rewardRepo.getById(id);
			return successRPCResponse({ ...reward, isComplete: reward.currentProgress >= reward.target });
		} catch (error) {
			console.error('Error while fetching reward by ID', error);
			return errorRPCResponse('REWARD_NOT_FOUND', 404);
		}
	}

	async getAllReward(): Promise<RPCResponse<RewardResponseType[]>> {
		try {
			const reward = await this.rewardRepo.getAll();
			return successRPCResponse(
				reward.map(reward => ({ ...reward, isComplete: reward.currentProgress >= reward.target }))
			);
		} catch (error) {
			console.error('Error on getAllReward', error);
			return errorRPCResponse('REWARD_NOT_FOUND', 404);
		}
	}

	async updateReward(
		rewardId: string,
		rewardData: Pick<RewardCreateType, 'currentProgress'>
	): Promise<RPCResponse<RewardResponseType>> {
		try {
			const currentReward = await this.rewardRepo.getById(rewardId);

			// Check if the reward is already completed (for both accumulation modes)
			if (currentReward.currentProgress >= currentReward.target) {
				return errorRPCResponse('REWARD_ALREADY_COMPLETED', 400);
			}

			// Validate that current progress doesn't exceed the target
			if (rewardData.currentProgress > currentReward.target) {
				return errorRPCResponse('CANNOT_UPDATE_REWARD', 400);
			}

			let newProgress = currentReward.currentProgress;

			switch (currentReward.accumulationMode) {
				case 'single':
					// If the reward is in 'single' mode, it can only be completed once
					newProgress = Math.min(currentReward.target, rewardData.currentProgress || 0);
					if (newProgress >= currentReward.target && currentReward.currentProgress >= 1) {
						return errorRPCResponse('PROGRESS_CANNOT_EXCEED_TARGET', 400);
					}
					break;
				case 'threshold':
					// In 'threshold' mode, progress accumulates but should not exceed the target
					newProgress += rewardData.currentProgress || 0;
					if (newProgress > currentReward.target) {
						return errorRPCResponse('PROGRESS_CANNOT_EXCEED_TARGET', 400);
					}
					break;
				default:
					return errorRPCResponse('INVALID_ACCUMULATION_MODE', 400);
			}

			// Step 4: Update the reward in the repository
			const updatedReward = await this.rewardRepo.update(rewardId, {
				currentProgress: newProgress,
			});

			// Step 5: Determine if the reward is now complete
			const isComplete = newProgress >= currentReward.target;

			// Return the updated reward with the isComplete flag
			return successRPCResponse({ ...updatedReward, isComplete });
		} catch (error) {
			console.error('Cannot update reward', error);
			return errorRPCResponse('CANNOT_UPDATE_REWARD');
		}
	}

	async incrementProgress(
		rewardId: string,
		byAmount: number
	): Promise<RPCResponse<RewardResponseType>> {
		try {
			const currentReward = await this.rewardRepo.getById(rewardId);

			// Check if the reward is already completed (for both accumulation modes)
			if (currentReward.currentProgress >= currentReward.target) {
				return errorRPCResponse('REWARD_ALREADY_COMPLETED', 400);
			}

			// Validate that the new progress does not exceed the target
			if (byAmount + currentReward.currentProgress > currentReward.target) {
				return errorRPCResponse('PROGRESS_CANNOT_EXCEED_TARGET', 400);
			}

			let newProgress = currentReward.currentProgress;

			switch (currentReward.accumulationMode) {
				case 'single':
					// If it's 'single' mode, progress can only be updated to reach the target once
					if (currentReward.currentProgress >= currentReward.target) {
						return errorRPCResponse('REWARD_ALREADY_COMPLETED', 400);
					}
					newProgress = Math.min(currentReward.target, currentReward.currentProgress + byAmount);
					break;
				case 'threshold':
					// In 'threshold' mode, progress accumulates but cannot exceed the target
					newProgress = Math.min(currentReward.target, currentReward.currentProgress + byAmount);
					if (newProgress > currentReward.target || newProgress < 0) {
						return errorRPCResponse('PROGRESS_CANNOT_EXCEED_TARGET', 400);
					}
					break;
				default:
					return errorRPCResponse('INVALID_ACCUMULATION_MODE', 400);
			}

			// Step 4: Update the reward in the repository
			const updatedReward = await this.rewardRepo.update(rewardId, {
				currentProgress: newProgress,
			});

			// Step 5: Determine if the reward is now complete
			const isComplete = newProgress >= currentReward.target;

			// Return the updated reward with the isComplete flag
			return successRPCResponse({ ...updatedReward, isComplete });
		} catch (error) {
			console.error('Cannot update reward', error);
			return errorRPCResponse('CANNOT_UPDATE_REWARD');
		}
	}

	async deleteReward(id: string): Promise<RPCResponse<{ message: string }>> {
		try {
			await this.rewardRepo.delete(id);
			return successRPCResponse({ message: 'SUCCESSFULLY_DELETED' });
		} catch (error) {
			console.error('Error while deleting reward', error);
			return errorRPCResponse('CANNOT_DELETE_REWARD');
		}
	}

	async deleteAll(): Promise<RPCResponse<{ message: string }>> {
		await this.ctx.storage.deleteAll();
		await this.ctx.storage.deleteAlarm();
		return successRPCResponse({ message: 'SUCCESSFULLY_DELETED' });
	}
}
