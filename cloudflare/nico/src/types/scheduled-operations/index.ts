import { z } from 'zod';

export const ScheduledOperationStatusSchema = z.enum(['pending', 'failed', 'completed']);

export const ScheduledOperationCreateSchema = z.object({
	type: z.enum([
		'group-context-expiry',
		'renew-subscription',
		'user-hard-delete',
		'send-weekly-engagement-email',
		'reset-withdrawal-data',
		'clean-up-completed-scheduled-operations',
		'clean-up-task-opportunity-reference',
	]), // Type of operation, e.g., "expire-group-context", "renew-subscription" etc...
	status: ScheduledOperationStatusSchema,
	domainId: z.string(),
	runAt: z.string().datetime('Invalid scheduled time'), // ISO date string
	data: z.string().optional(), // data that is required for the alarm event handling
});

export const ScheduledOperationRecordSchema = ScheduledOperationCreateSchema.extend({
	id: z.string().ulid('Invalid operation ID'),
	createdDate: z.string().datetime('Invalid created date'), // ISO date string for creation
	updatedDate: z.string().datetime('Invalid updated date'), // ISO date string for last update
});

export const GroupContextSchedulerDataSchema = z.object({
	groupId: z.string(),
	groupContextId: z.string(),
});

export const UserDeletionDataSchema = z.object({
	userId: z.string(),
});

export const SubscriptionRenewalSchedulerDataSchema = z.object({
	subscriptionId: z.string(),
	billingCycle: z.enum(['MONTHLY']),
});

export const ScheduledOperationSchema = ScheduledOperationRecordSchema.extend({
	isOverdue: z.boolean(), // Indicates if the operation is overdue
	data: z.union([GroupContextSchedulerDataSchema, SubscriptionRenewalSchedulerDataSchema]),
});

export type ScheduledOperationCreateType = z.infer<typeof ScheduledOperationCreateSchema>;
export type ScheduledOperationRecordType = z.infer<typeof ScheduledOperationRecordSchema>;
export type ScheduledOperation = z.infer<typeof ScheduledOperationSchema>;
export type ScheduledOperationStatus = z.infer<typeof ScheduledOperationStatusSchema>;
export type UserDeletionDataType = z.infer<typeof UserDeletionDataSchema>;
