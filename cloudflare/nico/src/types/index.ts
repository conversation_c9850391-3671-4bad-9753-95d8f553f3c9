import { ContentfulStatusCode } from 'hono/utils/http-status';
import { UserDataStoreManager } from '../durable-object';
import { ErrorCode } from '../app/localization';
export * from './group-context';
export * from './assignment';
export * from './rewards';
export * from './scheduled-operations';
export * from './subscription';

export type Environment = {
	WORKER_ENVIRONMENT: 'dev' | 'prod' | 'local';
	USER_DATA_MANAGER_DURABLE_OBJECT: DurableObjectNamespace<
		import('../durable-object').UserDataStoreManager
	>;
	CLOUDFLARE_INTEGRATION_USER_USERNAME: string;
	CLOUDFLARE_INTEGRATION_USER_PASSWORD: string;
	SUNNY_API_ENDPOINT: string;
	QUICKBUCKS_PRO_PRODUCT_ID: string;
	GROUP_CONTEXT_EXPIRY_DAYS: number;
	USER_DELETION_GRACE_PERIOD_DAYS: number;
	WITHDRAWAL_LIMITS_NOTSUBSCRIBED_LARGE: number;
	WITHDRAWAL_LIMITS_NOTSUBSCRIBED_SMALL: number;
	WITHDRAWAL_LIMIT_NOTSUBSCRIBED_USD_AMOUNT: number;
	WITHDRAWAL_LIMITS_SUBSCRIBED_SMALL: number;
	WITHDRAWAL_LIMITS_SUBSCRIBED_LARGE: number;
	SMALL_MINIMUM_PAYOUT_IN_DOLLARS: number;
	LARGE_MINIMUM_PAYOUT_IN_DOLLARS: number;
	SMALL_MINIMUM_PAYOUT_IN_POINTS: number;
	LARGE_MINIMUM_PAYOUT_IN_POINTS: number;
	EMAIL_ACTIVITY_QUEUE: Queue;
};

export type RPCFailureResponse = {
	success: false;
	data?: null;
	statusCode: ContentfulStatusCode;
	errorMessage: string;
	errorCode: ErrorCode;
};

export type RPCResponse<T> =
	| { success: true; data: T; statusCode: ContentfulStatusCode; error?: null }
	| RPCFailureResponse;

export type User = {
	userId: string;
	isDeleted: boolean;
};

export type NicoApp = {
	Bindings: Environment;
	Variables: {
		stub: DurableObjectStub<UserDataStoreManager>;
		user?: User;
		impersonatedUserId?: string;
	};
};

export type EmailActivityMessage = {
	userId: string;
	templateName: 'WEEKLY_EARNINGS';
};
