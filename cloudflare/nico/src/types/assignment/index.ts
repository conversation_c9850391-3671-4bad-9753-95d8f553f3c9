import { z } from 'zod';

export const AssignmentSchema = z.object({
	assignmentId: z.string(),
	platformId: z.string(),
	groupId: z.string(),
	groupContextId: z.string(),
});

export const AssignmentCreateSchema = z.object({
	assignmentId: z.string(),
	platformId: z.string(),
	groupId: z.string(),
});

export type AssignmentType = z.infer<typeof AssignmentSchema>;
export type AssignmentCreateType = z.infer<typeof AssignmentCreateSchema>;
