import { z } from 'zod';

export const AccumulatedAssignmentGroupData = z.object({
	completedAssignments: z.array(z.string()),
});

export type GroupType = '5for5' | '2for3';

export const GroupContextSchema = z.object({
	id: z.string(),
	groupId: z.string().nonempty(),
	rewardId: z.string().optional().nullable(),
	type: z.enum(['5for5', '2for3']),
	status: z.enum(['active', 'pending-payment', 'completed', 'expired']),
	createdDate: z.string(),
	updatedDate: z.string(),
	contextData: z.string(),
});

export const AccumulatorGroupContextDataWithStatsSchema = GroupContextSchema.extend({
	contextData: z.object({
		completedAssignments: z.array(z.string()),
		requiredCount: z.number(),
		isFulfilled: z.boolean(),
		completedCount: z.number(),
	}),
});

export const AccumulatorGroupContextDataSchema = GroupContextSchema.extend({
	contextData: AccumulatedAssignmentGroupData,
});

export const GroupContextCreateRequestSchema = z.object({
	groupId: z.string().nonempty(),
	type: z.enum(['5for5', '2for3']),
});

export const GroupContextCreateSchema = z.object({
	groupId: z.string().nonempty(),
	type: z.enum(['5for5', '2for3']),
	rewardId: z.string().optional(),
	status: z.enum(['active', 'pending-payment', 'completed', 'expired']).optional().nullable(),
	contextData: AccumulatedAssignmentGroupData.optional(),
});

export const GroupContextPatchSchema = z.object({
	status: z.enum(['completed', 'expired']).optional().nullable(),
	rewardId: z.string().optional(),
});

export type GroupContextRecordType = z.infer<typeof GroupContextSchema>;
export type GroupContextPatchType = z.infer<typeof GroupContextPatchSchema>;
export type GroupContextType = Omit<GroupContextRecordType, 'contextData'> & {
	contextData: object;
};
export type AccumulatorGroupContextDataType = z.infer<typeof AccumulatorGroupContextDataSchema>;

export type GroupContextCreateType = z.infer<typeof GroupContextCreateSchema>;
export type GroupContextCreateRequestType = z.infer<typeof GroupContextCreateRequestSchema>;

export type AccumulatedAssignmentGroupDataType = z.infer<typeof AccumulatedAssignmentGroupData>;
export type AccumulatedAssignmentGroupDataStatsType = z.infer<
	typeof AccumulatorGroupContextDataWithStatsSchema
>;
