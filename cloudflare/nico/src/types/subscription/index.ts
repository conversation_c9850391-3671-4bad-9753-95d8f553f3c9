import { z } from 'zod';

export const SubscriptionCreateRequestSchema = z.object({
	productId: z.string().min(1, 'type is required'),
	billingCycle: z.enum(['MONTHLY']),
	paymentType: z.enum(['REWARD_POINTS']),
});

export type SubscriptionRecord = {
	subscriptionId: string;
	productId: string; // Unique identifier for the subscription
	billingCycle: 'MONTHLY';
	paymentType: 'REWARD_POINTS';
	status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED';
	isAutoRenewalEnabled: number;
	expiresAt: string; // ISO 8601 timestamp for the expiration date
	startedAt: string; // ISO 8601 timestamp for the started date
	createdAt: string; // ISO 8601 timestamp
	updatedAt: string; // ISO 8601 timestamp
};

export interface Subscription extends Omit<SubscriptionRecord, 'isAutoRenewalEnabled'> {
	isAutoRenewalEnabled: boolean; // Overrides the isAutoRenewalEnabled property with a boolean type
}
export interface SubscriptionCreate extends Omit<Subscription, 'createdAt' | 'updatedAt'> {}
export type SubscriptionHistory = {
	id: string; // Unique identifier for the history entry
	productId: string; // Unique identifier for the productId
	subscriptionId: string; // The subscription being tracked
	eventType: string; // Type of the event (e.g., 'cancellation', 'renewal', etc.)
	eventTimestamp: string; // Timestamp when the event occurred
	previousStatus?: string | null; // The previous status of the subscription (if applicable)
	currentStatus: string; // The status after the event
	createdAt: string; // Timestamp when the record was created
};

export interface SubscriptionHistoryCreate {
	productId: string; // Unique identifier for the productId
	subscriptionId: string; // The subscription being tracked
	eventType: string; // Type of the event (e.g., 'cancellation', 'renewal', etc.)
	eventTimestamp: string; // Timestamp when the event occurred
	previousStatus?: string | null; // The previous status of the subscription (if applicable)
	currentStatus: string; // The status after the event
}

export type SubscriptionCreateRequest = z.infer<typeof SubscriptionCreateRequestSchema>;
