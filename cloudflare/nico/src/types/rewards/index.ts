import { z } from 'zod';

export const RewardProgressSchema = z.object({
	currentProgress: z.number(),
});

export const IncrementProgressSchema = z.object({
	currentProgressChange: z.number(),
});

// Define the schema for RewardCreateType
export const RewardCreateSchema = z.object({
	name: z.string().min(1, 'Name is required'),
	description: z.string().min(1, 'Description is required'),
	accumulationMode: z.enum(['single', 'threshold']),
	currentProgress: z.number().default(0),
	target: z.number().positive('Target must be a positive number'),
	value: z.number().positive('Value must be a positive number'),
	currency: z.string().min(1, 'Currency is required'),
});

// Define the schema for RewardType
export const RewardSchema = RewardCreateSchema.extend({
	id: z.string().ulid('Invalid reward ID'),
});

export const RewardResponseSchema = RewardCreateSchema.extend({
	isComplete: z.boolean(),
});

// Type aliases for convenience
export type RewardCreateType = z.infer<typeof RewardCreateSchema>;
export type RewardType = z.infer<typeof RewardSchema>;
export type RewardResponseType = z.infer<typeof RewardResponseSchema>;
