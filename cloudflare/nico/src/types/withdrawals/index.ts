import { z } from 'zod';

export type SubscriptionStatus = 'active' | 'expired' | 'inactive';

export const WithdrawalLimitSchema = z.object({
	smallWithdrawalCount: z.number(),
	largeWithdrawalCount: z.number(),
	accumulatedWithdrawalAmount: z.number(),
	lastResetDate: z.number(),
});

export type WithdrawalLimit = z.infer<typeof WithdrawalLimitSchema>;

export const PayoutTypeSchema = z.enum(['PAYPAL']); // Add 'stripe' here if it becomes relevant later.

export const AmountSchema = z.object({
	value: z.number(),
	currency: z.enum(['USD', 'REWARD_POINTS']), // A string representing the currency (e.g., "USD").
});

export const PayoutOrderRequestSchema = z.object({
	type: PayoutTypeSchema,
	amount: AmountSchema,
});

export type PayoutOrderRequest = z.infer<typeof PayoutOrderRequestSchema>;

export type WithdrawalProgress = {
	currentWithdrawalAmount: number;
	maximumWithdrawalAmount: number;
}

export type AllowedWithdrawalCount = {
	smallWithdrawalCount: number;
	largeWithdrawalCount: number;
	totalWithdrawalCount: number;
};

export type PayoutLimits = {
	largePayoutsUsed: number;
	smallPayoutsUsed: number;
	totalPayoutsLimit: number;
	withdrawalProgress: WithdrawalProgress | null;
};
