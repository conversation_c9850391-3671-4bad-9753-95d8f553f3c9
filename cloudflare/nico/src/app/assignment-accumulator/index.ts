import { GroupUserDataUpdateConflictError } from '../../errors';
import {
	AccumulatedAssignmentGroupDataStatsType,
	AccumulatedAssignmentGroupDataType,
	GroupType,
} from '../../types';

/**
 * This helper class is used when dealing with task completion and reconciliation
 * Used in - GroupDataManager.handleAssignmentCompletion
 * Used in - GroupDataManager.handleAssignmentRemove
 */
export class AssignmentAccumulatorHelper {
	static prepareInitialMetadata(): AccumulatedAssignmentGroupDataType {
		return { completedAssignments: [] };
	}

	static updateMetadata(
		existing: AccumulatedAssignmentGroupDataType,
		data: { assignmentId: string; action: 'add' | 'remove' }
	): AccumulatedAssignmentGroupDataType {
		const { assignmentId, action } = data;
		const { completedAssignments } = existing;
		const isAssignmentInList = completedAssignments.includes(assignmentId);

		if (action === 'add' && isAssignmentInList) {
			throw new GroupUserDataUpdateConflictError(
				`The assignment ${assignmentId} is already in the group context`
			);
		}

		if (action === 'remove' && !isAssignmentInList) {
			throw new GroupUserDataUpdateConflictError(
				`The assignment ${assignmentId} is not in the completed list`
			);
		}

		switch (action) {
			case 'add':
				return {
					...existing,
					completedAssignments: [...completedAssignments, assignmentId],
				};

			case 'remove':
				const updatedAssignments = completedAssignments.filter(id => id !== assignmentId);
				return {
					...existing,
					completedAssignments: updatedAssignments,
				};

			default:
				return existing;
		}
	}

	static getAccumulationStats(
		groupType: GroupType,
		metadata: AccumulatedAssignmentGroupDataType
	): AccumulatedAssignmentGroupDataStatsType['contextData'] {
		switch (groupType) {
			case '5for5':
				return {
					completedAssignments: metadata.completedAssignments,
					completedCount: metadata.completedAssignments.length,
					requiredCount: 5,
					isFulfilled: metadata.completedAssignments.length >= 5,
				};
			case '2for3':
				return {
					completedAssignments: metadata.completedAssignments,
					completedCount: metadata.completedAssignments.length,
					requiredCount: 2,
					isFulfilled: metadata.completedAssignments.length >= 2,
				};
		}
	}
}
