import { Environment } from '../../types';
import { Subscription } from '../../types/subscription';
import { SupportedLocales } from '../localization';

export const processPayment = async (
	env: Environment,
	userId: string,
	idempotencyKey: string,
	payload: {
		amount: { value: number; currency: 'USD' };
		type: 'REWARD_POINTS';
		description: string;
	},
	locale: SupportedLocales = 'en'
) => {
	const response = await fetch(`${env.SUNNY_API_ENDPOINT}/v1/internal/payments/payin`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'X-Impersonated-UserId': userId,
			'Idempotency-Key': idempotencyKey,
			'Accept-Language': locale,
			'Authorization': `Basic ${btoa(
				`${env.CLOUDFLARE_INTEGRATION_USER_USERNAME}:${env.CLOUDFLARE_INTEGRATION_USER_PASSWORD}`
			)}`,
		},
		body: JSON.stringify(payload),
	});
	return response;
};

export const createUserSubscription = async (
	env: Environment,
	userId: string,
	idempotencyKey: string,
	payload: Subscription,
	locale: SupportedLocales = 'en'
) => {
	const response = await fetch(`${env.SUNNY_API_ENDPOINT}/v1/internal/subscriptions`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'X-Impersonated-UserId': userId,
			'Idempotency-Key': idempotencyKey,
			'Accept-Language': locale,
			'Authorization': `Basic ${btoa(
				`${env.CLOUDFLARE_INTEGRATION_USER_USERNAME}:${env.CLOUDFLARE_INTEGRATION_USER_PASSWORD}`
			)}`,
		},
		body: JSON.stringify(payload),
	});

	if (!response.ok) {
		const errorMessage = await response.json();
		console.info('Could not subscribe:', { errorMessage, userId });
		throw new Error(JSON.stringify(errorMessage));
	}

	return response.json();
};

export const updateUserSubscription = async (
	env: Environment,
	userId: string,
	subscriptionId: string,
	idempotencyKey: string,
	payload: Subscription,
	locale: SupportedLocales = 'en'
) => {
	const response = await fetch(
		`${env.SUNNY_API_ENDPOINT}/v1/internal/subscriptions/${subscriptionId}`,
		{
			method: 'PATCH',
			headers: {
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': userId,
				'Idempotency-Key': idempotencyKey,
				'Accept-Language': locale,
				'Authorization': `Basic ${btoa(
					`${env.CLOUDFLARE_INTEGRATION_USER_USERNAME}:${env.CLOUDFLARE_INTEGRATION_USER_PASSWORD}`
				)}`,
			},
			body: JSON.stringify(payload),
		}
	);
	if (!response.ok) {
		const errorMessage = await response.json();
		console.error('Could not update subscription:', {
			payload: JSON.stringify(payload),
			errorMessage,
			userId,
		});
		throw new Error(JSON.stringify(errorMessage));
	}

	return response.json();
};

export const deleteUserSubscription = async (
	env: Environment,
	userId: string,
	productId: string,
	operationId: string,
	locale: SupportedLocales = 'en'
) => {
	const response = await fetch(`${env.SUNNY_API_ENDPOINT}/v1/internal/subscriptions/${productId}`, {
		method: 'DELETE',
		headers: {
			'Content-Type': 'application/json',
			'X-Impersonated-UserId': userId,
			'Idempotency-Key': `delete-subscription-${userId}-${operationId}`,
			'Accept-Language': locale,
			'Authorization': `Basic ${btoa(
				`${env.CLOUDFLARE_INTEGRATION_USER_USERNAME}:${env.CLOUDFLARE_INTEGRATION_USER_PASSWORD}`
			)}`,
		},
	});

	if (!response.ok) {
		const errorMessage = await response.json();
		console.error('Error updating subscription:', { errorMessage, userId });
		throw new Error(JSON.stringify(errorMessage));
	}
};
