import { Environment } from '../../types';
import { SupportedLocales } from '../localization';

export const softDeleteUser = async (
	env: Environment,
	userId: string,
	locale: SupportedLocales = 'en'
): Promise<Response> => {
	return fetch(`${env.SUNNY_API_ENDPOINT}/v1/internal/users/soft`, {
		method: 'DELETE',
		headers: {
			'Content-Type': 'application/json',
			'X-Impersonated-UserId': userId,
			'Accept-Language': locale,
			'Authorization': `Basic ${btoa(
				`${env.CLOUDFLARE_INTEGRATION_USER_USERNAME}:${env.CLOUDFLARE_INTEGRATION_USER_PASSWORD}`
			)}`,
		},
	});
};

export const forceDeleteUser = async (env: Environment, userId: string): Promise<Response> => {
	const response = await fetch(`${env.SUNNY_API_ENDPOINT}/v1/internal/users/hard`, {
		method: 'DELETE',
		headers: {
			'Content-Type': 'application/json',
			'X-Impersonated-UserId': userId,
			'Authorization': `Basic ${btoa(
				`${env.CLOUDFLARE_INTEGRATION_USER_USERNAME}:${env.CLOUDFLARE_INTEGRATION_USER_PASSWORD}`
			)}`,
		},
	});

	return response;
};

export const getUserInfo = async (env: Environment, userId: string): Promise<Response> => {
	const response = await fetch(`${env.SUNNY_API_ENDPOINT}/v1/internal/users`, {
		method: 'GET',
		headers: {
			'Content-Type': 'application/json',
			'X-Impersonated-UserId': userId,
			'Authorization': `Basic ${btoa(
				`${env.CLOUDFLARE_INTEGRATION_USER_USERNAME}:${env.CLOUDFLARE_INTEGRATION_USER_PASSWORD}`
			)}`,
		},
	});

	return response;
};

export const cleanupTaskOpportunityReference = async (
	env: Environment,
	userId: string,
	idempotencyKey: string
) => {
	const response = await fetch(
		`${env.SUNNY_API_ENDPOINT}/v1/internal/users/task_opportunity_references/cleanup`,
		{
			method: 'post',
			headers: {
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': userId,
				'Idempotency-Key': idempotencyKey,
				'Authorization': `Basic ${btoa(
					`${env.CLOUDFLARE_INTEGRATION_USER_USERNAME}:${env.CLOUDFLARE_INTEGRATION_USER_PASSWORD}`
				)}`,
			},
		}
	);

	if (!response.ok) {
		const errorMessage = await response.json();
		console.error('Error call to sunny cleanupTaskOpportunityReference:', { errorMessage, userId });
		throw new Error(JSON.stringify(errorMessage));
	}
};
