export const localizationMessages = {
	en: {
		// Existing translations in English
		GROUP_CONTEXT_NOT_FOUND: 'Group context not found',
		UNAUTHENTICATED: 'Unauthenticated',
		IDEMPOTENCY_KEY_SHOULD_BE_PRESENT: 'Idempotency-Key header is missing',
		SUBSCRIPTION_ALREADY_EXISTS: 'You have already subscribed to this package',
		SUCCESSFULLY_DELETED: 'Successfully deleted',
		FAILED_CREATE_GROUP_CONTEXT: 'Failed to create group context',
		INVALID_STATUS_UPDATE: "Invalid status update. Only 'completed' or 'expired' are allowed",
		CANNOT_MARK_AS_COMPLETED:
			"Cannot mark group context as completed when current status is '{status}'",
		REWARD_NOT_FOUND: 'Reward with Id not found',
		CANNOT_CREATE_REWARD: 'Error while creating reward',
		CANNOT_MARK_AS_EXPIRED:
			"Cannot mark group context as expired when current status is '{status}'",
		INVALID_STATUS_COMPLETED:
			"Invalid status. Cannot mark as 'completed' unless current status is 'pending-payment'",
		INVALID_STATUS_EXPIRED:
			"Invalid status. Cannot mark as 'expired' unless current status is 'active'",
		GROUP_ALREADY_COMPLETED: 'Group accumulation already completed',
		GROUP_CONTEXT_NOT_ACTIVE: 'Group context is not active',
		FAILED_UPDATE_GROUP_CONTEXT: 'Failed to update group context',
		FAILED_PATCH_GROUP_CONTEXT: 'Failed to patch the latest group context',
		ERROR_HANDLING_ASSIGNMENT_COMPLETION: 'Error handling assignment completion',
		ERROR_HANDLING_ASSIGNMENT_REMOVE: 'Error handling assignment removal',
		ERROR_UPDATE_GROUP_CONTEXT: 'Error updating group context',
		INVALID_ACCUMULATION_MODE: 'Invalid accumulation mode',
		REWARD_ALREADY_COMPLETED: 'Reward has already been completed',
		CANNOT_UPDATE_REWARD: 'Cannot update reward',
		CANNOT_DELETE_REWARD: 'Cannot delete reward',
		PROGRESS_CANNOT_EXCEED_TARGET: 'Progress cannot exceed target and it cannot be negative',
		ACTIVE_GROUP_CONTEXT_ALREADY_EXISTS: 'Active group context already exists',
		CANNOT_UPDATE_REWARD_ID: 'Cannot update rewardId. RewardId overriding is prohibited',
		INTERNAL_ERROR: 'Something went wrong',
		WITHDRAWAL_LIMIT_NOT_FOUND: 'Withdrawal limit not found',
		CAN_ONLY_MAKE_A_TOTAL_OF_X_WITHDRAWALS_A_MONTH:
			'You can only make a total of {numWithdrawals} withdrawals a month',
		CAN_MAKE_ONLY_1_WITHDRAWAL_LESS_THAN_X_POINTS_IN_A_MONTH:
			'Withdrawals below {amount} points are limited to one per month.',
		CAN_ONLY_MAKE_WITHDRAWS_TO_X_AMOUNT_IN_A_MONTH:
			'Withdrawals are limited to ${maximumAmount} per month for unverified users.',
		PAYOUT_AMOUNT_CANNOT_BE_LESS_THAN_X_POINTS: 'Payout amount cannot be less than {amount} points',
	},
	pt: {
		// Updated Portuguese translations
		GROUP_CONTEXT_NOT_FOUND: 'Contexto de grupo não encontrado',
		UNAUTHENTICATED: 'Não autenticado',
		IDEMPOTENCY_KEY_SHOULD_BE_PRESENT: 'O cabeçalho Idempotency-Key está ausente.',
		SUBSCRIPTION_ALREADY_EXISTS: 'Você já assinou este pacote.',
		SUCCESSFULLY_DELETED: 'Excluído com sucesso',
		FAILED_CREATE_GROUP_CONTEXT: 'Falha ao criar contexto de grupo',
		INVALID_STATUS_UPDATE:
			"Atualização de status inválida. Apenas 'completo' ou 'expirado' são permitidos",
		CANNOT_MARK_AS_COMPLETED:
			"Não é possível marcar o contexto de grupo como completo quando o status atual é '{status}'",
		CANNOT_MARK_AS_EXPIRED:
			"Não é possível marcar o contexto de grupo como expirado quando o status atual é '{status}'",
		INVALID_STATUS_COMPLETED:
			"Status inválido. Não pode ser marcado como 'completo' a menos que o status atual seja 'pendente de pagamento'",
		INVALID_STATUS_EXPIRED:
			"Status inválido. Não pode ser marcado como 'expirado' a menos que o status atual seja 'ativo'",
		GROUP_ALREADY_COMPLETED: 'A acumulação do grupo já foi concluída',
		GROUP_CONTEXT_NOT_ACTIVE: 'O contexto de grupo não está ativo',
		FAILED_UPDATE_GROUP_CONTEXT: 'Falha ao atualizar o contexto de grupo',
		FAILED_PATCH_GROUP_CONTEXT: 'Falha ao atualizar o último contexto de grupo',
		REWARD_NOT_FOUND: 'Recompensa com Id não encontrada',
		CANNOT_CREATE_REWARD: 'Erro ao criar a recompensa',
		ERROR_HANDLING_ASSIGNMENT_COMPLETION: 'Erro ao concluir a tarefa',
		ERROR_HANDLING_ASSIGNMENT_REMOVE: 'Erro ao remover a tarefa',
		ERROR_UPDATE_GROUP_CONTEXT: 'Erro ao atualizar o contexto de grupo',
		INVALID_ACCUMULATION_MODE: 'Modo de acumulação inválido',
		REWARD_ALREADY_COMPLETED: 'Recompensa já foi concluída',
		CANNOT_UPDATE_REWARD: 'Não é possível atualizar a recompensa',
		CANNOT_DELETE_REWARD: 'Não é possível excluir a recompensa',
		WITHDRAWAL_LIMIT_NOT_FOUND: 'Limite de saque não encontrado',
		PROGRESS_CANNOT_EXCEED_TARGET: 'O progresso não pode exceder o alvo e não pode ser negativo',
		ACTIVE_GROUP_CONTEXT_ALREADY_EXISTS: 'O contexto do grupo ativo já existe',
		CANNOT_UPDATE_REWARD_ID:
			'Não é possível atualizar o rewardId. A substituição do rewardId é proibida.',
		INTERNAL_ERROR: 'Algo deu errado',
		CAN_ONLY_MAKE_A_TOTAL_OF_X_WITHDRAWALS_A_MONTH:
			'Você só pode fazer um total de {numWithdrawals} saques por mês',
		CAN_MAKE_ONLY_1_WITHDRAWAL_LESS_THAN_X_POINTS_IN_A_MONTH:
			'Saques abaixo de {amount} pontos estão limitados a um por mês.',
		CAN_ONLY_MAKE_WITHDRAWS_TO_X_AMOUNT_IN_A_MONTH:
			'Os saques estão limitados a ${maximumAmount} por mês para usuários não verificados.',
		PAYOUT_AMOUNT_CANNOT_BE_LESS_THAN_X_POINTS:
			'O valor do pagamento não pode ser inferior a {amount} pontos',
	},
	es: {
		// Updated Spanish translations
		GROUP_CONTEXT_NOT_FOUND: 'Contexto de grupo no encontrado',
		UNAUTHENTICATED: 'No autenticado',
		IDEMPOTENCY_KEY_SHOULD_BE_PRESENT: 'Falta el encabezado Idempotency-Key.',
		SUBSCRIPTION_ALREADY_EXISTS: 'Ya te has suscrito a este paquete.',
		SUCCESSFULLY_DELETED: 'Eliminado con éxito',
		FAILED_CREATE_GROUP_CONTEXT: 'Error al crear el contexto de grupo',
		INVALID_STATUS_UPDATE:
			"Actualización de estado no válida. Solo se permiten 'completado' o 'expirado'",
		CANNOT_MARK_AS_COMPLETED:
			"No se puede marcar el contexto del grupo como completado cuando el estado actual es '{status}'",
		REWARD_NOT_FOUND: 'Recompensa con Id no encontrada',
		CANNOT_CREATE_REWARD: 'Error al crear la recompensa',
		CANNOT_MARK_AS_EXPIRED:
			"No se puede marcar el contexto del grupo como expirado cuando el estado actual es '{status}'",
		INVALID_STATUS_COMPLETED:
			"Estado no válido. No se puede marcar como 'completado' a menos que el estado actual sea 'pendiente de pago'",
		INVALID_STATUS_EXPIRED:
			"Estado no válido. No se puede marcar como 'expirado' a menos que el estado actual sea 'activo'",
		GROUP_ALREADY_COMPLETED: 'La acumulación del grupo ya se ha completado',
		GROUP_CONTEXT_NOT_ACTIVE: 'El contexto del grupo no está activo',
		FAILED_UPDATE_GROUP_CONTEXT: 'Error al actualizar el contexto del grupo',
		FAILED_PATCH_GROUP_CONTEXT: 'Error al actualizar el último contexto del grupo',
		ERROR_HANDLING_ASSIGNMENT_COMPLETION: 'Error al completar la asignación',
		ERROR_HANDLING_ASSIGNMENT_REMOVE: 'Error al eliminar la asignación',
		ERROR_UPDATE_GROUP_CONTEXT: 'Error al actualizar el contexto del grupo',
		INVALID_ACCUMULATION_MODE: 'Modo de acumulación no válido',
		REWARD_ALREADY_COMPLETED: 'La recompensa ya se ha completado',
		CANNOT_UPDATE_REWARD: 'No se puede actualizar la recompensa',
		CANNOT_DELETE_REWARD: 'No se puede eliminar la recompensa',
		WITHDRAWAL_LIMIT_NOT_FOUND: 'Límite de retiro no encontrado',
		PROGRESS_CANNOT_EXCEED_TARGET:
			'El progreso no puede exceder el objetivo y no puede ser negativo',
		ACTIVE_GROUP_CONTEXT_ALREADY_EXISTS: 'El contexto del grupo activo ya existe',
		CANNOT_UPDATE_REWARD_ID:
			'No se puede actualizar el rewardId. La sobrescritura del rewardId está prohibida.',
		INTERNAL_ERROR: 'Algo salió mal',
		CAN_ONLY_MAKE_A_TOTAL_OF_X_WITHDRAWALS_A_MONTH:
			'Solo puede hacer un total de {numWithdrawals} retiros al mes',
		CAN_MAKE_ONLY_1_WITHDRAWAL_LESS_THAN_X_POINTS_IN_A_MONTH:
			'Los retiros por debajo de {amount} puntos están limitados a uno por mes.',
		CAN_ONLY_MAKE_WITHDRAWS_TO_X_AMOUNT_IN_A_MONTH:
			'Los retiros están limitados a ${maximumAmount} por mes para usuarios no verificados.',
		PAYOUT_AMOUNT_CANNOT_BE_LESS_THAN_X_POINTS:
			'El monto del pago no puede ser inferior a {amount} puntos',
	},
};

export type SupportedLocales = keyof typeof localizationMessages;
export type ErrorCode = keyof (typeof localizationMessages)['en'];
