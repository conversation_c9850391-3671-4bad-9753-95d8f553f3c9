import { basicAuth } from 'hono/basic-auth';
import { MiddlewareHandler } from 'hono';
import { NicoApp } from '../../types';

export const basicAuthMiddleware: MiddlewareHandler<NicoApp> = async (c, next) => {
	const username = c.env.CLOUDFLARE_INTEGRATION_USER_USERNAME;
	const password = c.env.CLOUDFLARE_INTEGRATION_USER_PASSWORD;
	try {
		await basicAuth({ username, password })(c, next);
	} catch (e) {
		return c.json({ status: 'error', message: 'Authentication failed' }, 401);
	}
};
