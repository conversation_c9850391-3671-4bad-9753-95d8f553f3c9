import { Middleware<PERSON><PERSON>ler } from 'hono';
import { z } from 'zod';
import { NicoApp } from '../../types';
import { formatErrorResponse } from '../../utils';

const LocationHintSchema = z
	.enum(['wnam', 'enam', 'sam', 'weur', 'eeur', 'apac', 'oc', 'afr', 'me'])
	.optional();

export const bindUserDurableObjectStub: MiddlewareHandler<NicoApp> = async (c, next) => {
	// internal APIs uses impersonatedUserId
	// external API uses user
	const userId = c.get('impersonatedUserId') ?? c.get('user')?.userId;
	const locationHint = LocationHintSchema.parse(c.req.header()['X-Location-Hint']);
	if (userId == null) {
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				detail: [{ errorCode: 'unauthorized', message: 'Unauthorized access' }],
				statusCode: 401,
			}),
			401
		);
	}

	const id = c.env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName(userId);
	const stub = c.env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id, {
		locationHint,
	});
	await stub.storeDurableObjectName(userId);
	c.set('stub', stub);
	await next();
};
