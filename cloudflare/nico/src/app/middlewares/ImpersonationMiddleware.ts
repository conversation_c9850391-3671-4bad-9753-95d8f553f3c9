import { MiddlewareHandler } from 'hono';
import { NicoApp } from '../../types';
import { formatErrorResponse } from '../../utils';

export const impersonationMiddleware: MiddlewareHandler<NicoApp> = async (c, next) => {
	const impersonatedUserId = c.req.header('X-Impersonated-UserId');
	if (!impersonatedUserId) {
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				detail: [
					{
						errorCode: 'USER_ID_NOT_PROVIDED',
						message: 'X-Impersonated-UserId header is required',
					},
				],
				statusCode: 400,
			}),
			400
		);
	}
	c.set('impersonatedUserId', impersonatedUserId);
	await next();
};
