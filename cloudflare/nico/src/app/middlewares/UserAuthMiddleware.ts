import { MiddlewareHandler } from 'hono';
import { createErrorDetail, formatErrorResponse } from '../../utils';
import { NicoApp, User } from '../../types';
import { SupportedLocales } from '../localization';

export const userAuthMiddleware: MiddlewareHandler<NicoApp> = async (c, next) => {
	// Extract headers from the current request
	const headers = new Headers(c.req.header());

	const locale = (c.req.header('Accept-Language') ?? 'en') as SupportedLocales;
	const hasAuthorizationHeader = headers.has('X-Auth-Token');

	const endpoint = hasAuthorizationHeader
		? `${c.env.SUNNY_API_ENDPOINT}/v1/users`
		: `${c.env.SUNNY_API_ENDPOINT}/web/v1/users`;

	// Perform the fetch call to the determined endpoint with the same headers
	const userResponse = await fetch(endpoint, {
		method: 'GET',
		headers: headers,
	});
	if (userResponse.status === 401 || !userResponse.ok) {
		// Handle unauthorized access
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				statusCode: 401,
				detail: [createErrorDetail('UNAUTHENTICATED', locale)],
			}),
			401
		);
	}
	const user = (await userResponse.json()) as User;
	c.set('user', user);
	// If authorized, proceed to the next middleware or route handler
	await next();
};
