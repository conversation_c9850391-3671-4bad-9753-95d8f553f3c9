import { Hono } from 'hono';
import { NicoApp } from '../../types';
import {
	bindUserDurableObjectStub,
	impersonationMiddleware,
	basicAuthMiddleware,
} from '../middlewares/';
import { formatErrorResponse, formatSuccessResponse } from '../../utils';

const subscriptionHistoryHandler = new Hono<NicoApp>();

subscriptionHistoryHandler.use('*', basicAuthMiddleware);
subscriptionHistoryHandler.use('*', impersonationMiddleware);
subscriptionHistoryHandler.use('*', bindUserDurableObjectStub);

subscriptionHistoryHandler.get('/', async c => {
	const stub = c.get('stub');
	const subscriptionId = c.req.query('subscriptionId');
	const productId = c.req.query('productId');
	if (subscriptionId) {
		const historyItems = await stub.getSubscriptionHistoryBySubscriptionId(subscriptionId);
		return c.json(formatSuccessResponse(historyItems));
	}
	if (productId) {
		const historyItems = await stub.getSubscriptionHistoryByProductId(productId);
		return c.json(formatSuccessResponse(historyItems));
	}
	return c.json(
		formatErrorResponse({
			path: c.req.path,
			method: c.req.method,
			detail: [
				{
					errorCode: 'BAD_REQUEST',
					message: 'Either subscriptionId or productId must be included in the query parameter',
				},
			],
			statusCode: 400,
		})
	);
});

export { subscriptionHistoryHandler as SubscriptionHistoryHandler };
