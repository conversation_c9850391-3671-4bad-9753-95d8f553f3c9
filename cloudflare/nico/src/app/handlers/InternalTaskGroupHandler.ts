import { Hono } from 'hono';
import {
	AssignmentCreateSchema,
	GroupContextCreateRequestSchema,
	GroupContextPatchSchema,
	NicoApp,
} from '../../types';
import {
	basicAuthMiddleware,
	bindUserDurableObjectStub,
	impersonationMiddleware,
} from '../middlewares';
import { formatErrorResponse, formatSuccessResponse } from '../../utils';

const app = new Hono<NicoApp>();

app.use('*', basicAuthMiddleware);
app.use('*', impersonationMiddleware);
app.use('*', bindUserDurableObjectStub);

app.post('/:groupId/contexts', async c => {
	const { groupId } = c.req.param();
	const groupCreate = GroupContextCreateRequestSchema.parse(await c.req.json());
	const stub = c.get('stub');
	const groupContext = await stub.handleCreateGroupContext(groupId, groupCreate);

	if (!groupContext.success) {
		console.error(`could not create group context ${JSON.stringify(groupContext)}`);
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				detail: [{ errorCode: groupContext.errorCode, message: groupContext.errorMessage }],
				statusCode: groupContext.statusCode,
			}),
			groupContext.statusCode
		);
	}
	return c.json(formatSuccessResponse(groupContext.data, groupContext.statusCode));
});

app.post('/:groupId/contexts/latest/assignments', async c => {
	const { groupId } = c.req.param();
	const assignmentData = AssignmentCreateSchema.parse(await c.req.json());
	const stub = c.get('stub');
	const groupContext = await stub.handleAssignmentCompletion(groupId, assignmentData);

	if (!groupContext.success) {
		console.error(`could not add assignment ${JSON.stringify(groupContext)}`);
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				detail: [{ errorCode: groupContext.errorCode, message: groupContext.errorMessage }],
				statusCode: groupContext.statusCode,
			}),
			groupContext.statusCode
		);
	}
	return c.json(formatSuccessResponse(groupContext.data, groupContext.statusCode));
});

app.patch('/:groupId/contexts/latest', async c => {
	const { groupId } = c.req.param();
	const stub = c.get('stub');
	const assignmentPartial = GroupContextPatchSchema.parse(await c.req.json());
	const groupContext = await stub.patchLatestGroupContext(groupId, assignmentPartial);

	if (!groupContext.success) {
		console.error(`could not patch group context ${JSON.stringify(groupContext)}`);
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				detail: [{ errorCode: groupContext.errorCode, message: groupContext.errorMessage }],
				statusCode: groupContext.statusCode,
			}),
			groupContext.statusCode
		);
	}
	return c.json(formatSuccessResponse(groupContext.data, groupContext.statusCode));
});

app.delete('/:groupId/contexts/assignments/:assignmentId', async c => {
	const { assignmentId } = c.req.param();
	const stub = c.get('stub');
	const deleteResponse = await stub.handleAssignmentRemove(assignmentId);

	if (deleteResponse.success) {
		return c.json(formatSuccessResponse(deleteResponse.data, deleteResponse.statusCode));
	}
	console.error(`could not patch group context ${JSON.stringify(deleteResponse)}`);
	return c.json(
		formatErrorResponse({
			path: c.req.path,
			method: c.req.method,
			detail: [{ errorCode: deleteResponse.errorCode, message: deleteResponse.errorMessage }],
			statusCode: deleteResponse.statusCode,
		}),
		deleteResponse.statusCode
	);
});

app.get('/:groupId/contexts/latest', async c => {
	const { groupId } = c.req.param();
	const stub = c.get('stub');
	const contextData = await stub.getLatestGroupContext(groupId);

	if (contextData.success) {
		return c.json(formatSuccessResponse(contextData.data, contextData.statusCode));
	}
	return c.json(
		formatErrorResponse({
			path: c.req.path,
			method: c.req.method,
			detail: [{ errorCode: contextData.errorCode, message: contextData.errorMessage }],
			statusCode: contextData.statusCode,
		}),
		contextData.statusCode
	);
});

app.get('/:groupId/contexts', async c => {
	const { groupId } = c.req.param();

	const stub = c.get('stub');
	const groupContextsResponse = await stub.getAllGroupContextsByGroupId(groupId);

	if (groupContextsResponse.success) {
		return c.json(
			formatSuccessResponse(groupContextsResponse.data, groupContextsResponse.statusCode)
		);
	}
	return c.json(
		formatErrorResponse({
			path: c.req.path,
			method: c.req.method,
			detail: [
				{
					errorCode: groupContextsResponse.errorCode,
					message: groupContextsResponse.errorMessage,
				},
			],
			statusCode: groupContextsResponse.statusCode,
		}),
		groupContextsResponse.statusCode
	);
});

app.get('/contexts', async c => {
	const stub = c.get('stub');
	const groupContextsResponse = await stub.getAllGroupContexts();

	if (groupContextsResponse.success) {
		return c.json(
			formatSuccessResponse(groupContextsResponse.data, groupContextsResponse.statusCode)
		);
	}
	return c.json(
		formatErrorResponse({
			path: c.req.path,
			method: c.req.method,
			detail: [
				{
					errorCode: groupContextsResponse.errorCode,
					message: groupContextsResponse.errorMessage,
				},
			],
			statusCode: groupContextsResponse.statusCode,
		}),
		groupContextsResponse.statusCode
	);
});

app.get('/:groupId/contexts/:contextId', async c => {
	const { contextId } = c.req.param();

	const stub = c.get('stub');
	const contextData = await stub.getGroupContextById(contextId);

	if (contextData.success) {
		return c.json(formatSuccessResponse(contextData.data, contextData.statusCode));
	}
	return c.json(
		formatErrorResponse({
			path: c.req.path,
			method: c.req.method,
			detail: [{ errorCode: contextData.errorCode, message: contextData.errorMessage }],
			statusCode: contextData.statusCode,
		}),
		contextData.statusCode
	);
});

app.patch('/:groupId/contexts/:contextId', async c => {
	const { contextId } = c.req.param();
	const stub = c.get('stub');
	const assignmentPartial = GroupContextPatchSchema.parse(await c.req.json());
	const groupContext = await stub.patchGroupContext(contextId, assignmentPartial);

	if (!groupContext.success) {
		console.error(`could not patch group context ${contextId}: request- ${JSON.stringify(groupContext)}`);
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				detail: [{ errorCode: groupContext.errorCode, message: groupContext.errorMessage }],
				statusCode: groupContext.statusCode,
			}),
			groupContext.statusCode
		);
	}
	return c.json(formatSuccessResponse(groupContext.data, groupContext.statusCode));
});

app.delete('/:groupId/contexts/:contextId', async c => {
	const { contextId } = c.req.param();

	const stub = c.get('stub');
	const contextData = await stub.deleteGroupContextById(contextId);

	if (contextData.success) {
		return new Response(null, { status: 204 });
	}
	console.error(`could not delete group context ${contextData}`);
	return c.json(
		formatErrorResponse({
			path: c.req.path,
			method: c.req.method,
			detail: [{ errorCode: contextData.errorCode, message: contextData.errorMessage }],
			statusCode: contextData.statusCode,
		}),
		contextData.statusCode
	);
});

export { app as InternalTaskGroupHandler };
