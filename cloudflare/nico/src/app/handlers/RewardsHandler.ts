import { Hono } from 'hono';
import {
	RewardCreateSchema,
	RewardProgressSchema,
	IncrementProgressSchema,
	NicoApp,
} from '../../types';
import {
	bindUserDurableObjectStub,
	impersonationMiddleware,
	basicAuthMiddleware,
} from '../middlewares/';
import { formatErrorResponse, formatSuccessResponse } from '../../utils';

const rewardsHandler = new Hono<NicoApp>();

rewardsHandler.use('*', basicAuthMiddleware);
rewardsHandler.use('*', impersonationMiddleware);
rewardsHandler.use('*', bindUserDurableObjectStub);

rewardsHandler.post('/', async c => {
	const rewardData = RewardCreateSchema.parse(await c.req.json());
	const stub = c.get('stub');
	const rewardResponse = await stub.createReward(rewardData);

	if (!rewardResponse.success) {
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				detail: [{ errorCode: rewardResponse.errorCode, message: rewardResponse.errorMessage }],
				statusCode: rewardResponse.statusCode,
			}),
			rewardResponse.statusCode
		);
	}
	return c.json(formatSuccessResponse(rewardResponse.data, 201), 201);
});

rewardsHandler.get('/', async c => {
	const stub = c.get('stub');
	const rewardResponse = await stub.getAllReward();

	if (!rewardResponse.success) {
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				detail: [{ errorCode: rewardResponse.errorCode, message: rewardResponse.errorMessage }],
				statusCode: rewardResponse.statusCode,
			}),
			rewardResponse.statusCode
		);
	}
	return c.json(formatSuccessResponse(rewardResponse.data));
});

rewardsHandler.get('/:rewardId', async c => {
	const { rewardId } = c.req.param();
	const stub = c.get('stub');
	const rewardResponse = await stub.getRewardById(rewardId);

	if (!rewardResponse.success) {
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				detail: [{ errorCode: rewardResponse.errorCode, message: rewardResponse.errorMessage }],
				statusCode: rewardResponse.statusCode,
			}),
			rewardResponse.statusCode
		);
	}
	return c.json(formatSuccessResponse(rewardResponse.data, 200));
});

rewardsHandler.patch('/:rewardId', async c => {
	const { rewardId } = c.req.param();
	const progressData = RewardProgressSchema.parse(await c.req.json());
	const stub = c.get('stub');
	const progressResponse = await stub.updateReward(rewardId, progressData);

	if (!progressResponse.success) {
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				detail: [{ errorCode: progressResponse.errorCode, message: progressResponse.errorMessage }],
				statusCode: progressResponse.statusCode,
			}),
			progressResponse.statusCode
		);
	}
	return c.json(formatSuccessResponse(progressResponse.data, 200));
});

rewardsHandler.post('/:rewardId/progress', async c => {
	const { rewardId } = c.req.param();
	const incrementData = IncrementProgressSchema.parse(await c.req.json());
	const stub = c.get('stub');
	const incrementResponse = await stub.incrementProgress(
		rewardId,
		incrementData.currentProgressChange
	);

	if (!incrementResponse.success) {
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				detail: [
					{ errorCode: incrementResponse.errorCode, message: incrementResponse.errorMessage },
				],
				statusCode: incrementResponse.statusCode,
			}),
			incrementResponse.statusCode
		);
	}
	return c.json(formatSuccessResponse(incrementResponse.data, 200));
});

rewardsHandler.post('/:rewardId/reset', async c => {
	const { rewardId } = c.req.param();
	const stub = c.get('stub');
	const resetResponse = await stub.updateReward(rewardId, { currentProgress: 0 });

	if (!resetResponse.success) {
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				detail: [{ errorCode: resetResponse.errorCode, message: resetResponse.errorMessage }],
				statusCode: resetResponse.statusCode,
			}),
			resetResponse.statusCode
		);
	}
	return c.json(formatSuccessResponse(resetResponse.data, 200));
});

export { rewardsHandler as RewardsHandler };
