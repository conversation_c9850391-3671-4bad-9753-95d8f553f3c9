import { Hono } from 'hono';
import { NicoApp, ScheduledOperationStatusSchema } from '../../types';
import { basicAuthMiddleware, bindUserDurableObjectStub } from '../middlewares';
import { formatSuccessResponse } from '../../utils';
import { impersonationMiddleware } from '../middlewares/ImpersonationMiddleware';

const app = new Hono<NicoApp>();

app.get('/', basicAuthMiddleware, impersonationMiddleware, bindUserDurableObjectStub, async c => {
	const stub = c.get('stub');

	const status = ScheduledOperationStatusSchema.optional().parse(c.req.query('status'));
	const schOperations = await stub.getScheduledOperations(status);
	return c.json(formatSuccessResponse(schOperations));
});

export { app as ScheduledOperationHandler };
