import { Hono } from 'hono';
import { <PERSON>App } from '../../types';
import { userAuthMiddleware } from '../middlewares';
import { formatErrorResponse, formatSuccessResponse } from '../../utils';
import { bindUserDurableObjectStub } from '../middlewares/BindUserDurableObject';

const app = new Hono<NicoApp>();

app.get('/:groupId/contexts/latest', userAuthMiddleware, bindUserDurableObjectStub, async c => {
	const { groupId } = c.req.param();
	const { enableStats } = c.req.query();
	const stub = c.get('stub');
	const contextData = await stub.getLatestGroupContext(groupId, Boolean(enableStats));

	if (contextData.success) {
		return c.json(formatSuccessResponse(contextData.data, contextData.statusCode));
	}
	return c.json(
		formatErrorResponse({
			path: c.req.path,
			method: c.req.method,
			detail: [{ errorCode: contextData.errorCode, message: contextData.errorMessage }],
			statusCode: contextData.statusCode,
		}),
		contextData.statusCode
	);
});

export { app as TaskGroupHandler };
