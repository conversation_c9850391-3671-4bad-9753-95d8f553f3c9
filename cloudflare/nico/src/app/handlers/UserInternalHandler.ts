import { Hono } from 'hono';
import {
	basicAuthMiddleware,
	impersonationMiddleware,
	bindUserDurableObjectStub,
} from '../middlewares';
import { NicoApp } from '../../types';
import { formatErrorResponse, formatSuccessResponse } from '../../utils';
import { ContentfulStatusCode } from 'hono/utils/http-status';

const app = new Hono<NicoApp>();

app
	.use('*', basicAuthMiddleware)
	.use('*', impersonationMiddleware)
	.use('*', bindUserDurableObjectStub)
	.delete('/', async c => {
		const stub = c.get('stub');
		const data = await stub.deleteAll();
		if (data.success) {
			return new Response(null, { status: 204 });
		}
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				detail: [{ errorCode: data.errorCode, message: data.errorMessage }],
				statusCode: data.statusCode,
			}),
			data.statusCode as ContentfulStatusCode
		);
	})
	.get('/alarm', async c => {
		const stub = c.get('stub');
		const data = await stub.getScheduledAlarm();
		if (data.success) {
			return c.json(data.data);
		}
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				detail: [{ errorCode: data.errorCode, message: data.errorMessage }],
				statusCode: data.statusCode,
			}),
			data.statusCode as ContentfulStatusCode
		);
	})
	.post('/scheduled_operations/:scheduledOperationId/run', async c => {
		const stub = c.get('stub');
		try {
			await stub.executeScheduledOperationWithId(c.req.param('scheduledOperationId'));
			return c.json({ message: 'Successfully executed scheduledOperation' });
		} catch (error) {
			throw error;
		}
	})
	.get('/payout_limits', async c => {
		const stub = c.get('stub');
		try {
			const withdrawalLimits = await stub.getWithdrawalLimit();
			return c.json(withdrawalLimits);
		} catch (error) {
			throw error;
		}
	})
	.get('/init', async c => {
		const stub = c.get('stub');
		await stub.runMigrations();
		return c.json(formatSuccessResponse({ message: 'Success' }), 200);
	});

export { app as UserDurableObjectHandler };
