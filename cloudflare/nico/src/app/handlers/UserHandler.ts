import { Hono } from 'hono';
import dayjs from 'dayjs';
import { ulidFactory } from 'ulid-workers';
import { ContentfulStatusCode, StatusCode } from 'hono/utils/http-status';
import { NicoApp, ScheduledOperationCreateType, UserDeletionDataType } from '../../types';
import {
	userAuthMiddleware,
	bindUserDurableObjectStub,
	zValidatorWithLogging,
} from '../middlewares';
import {
	AllowedWithdrawalCount,
	PayoutOrderRequest,
	PayoutOrderRequestSchema,
	WithdrawalLimit,
} from '../../types/withdrawals';
import {
	createErrorDetail,
	ErrorResponseType,
	formatErrorResponse,
	formatSuccessResponse,
	getRewardPointsFromPayoutOrder, getUSDValueFromPayoutOrder,
} from '../../utils';
import { hasActiveSubscription, getAllowedWithdrawalCount } from '../../utils/withdrawalHelpers';
import { SupportedLocales } from '../localization';
import { softDeleteUser } from '../apis/users';

const app = new Hono<NicoApp>();

app.post(
	'/:userId/payouts',
	zValidatorWithLogging('json', PayoutOrderRequestSchema),
	userAuthMiddleware,
	bindUserDurableObjectStub,
	async c => {
		const user = c.get('user');
		const locale = (c.req.header('Accept-Language') ?? 'en') as SupportedLocales;
		if (user?.userId !== c.req.param().userId) {
			return c.json(
				formatErrorResponse({
					path: c.req.path,
					method: c.req.method,
					statusCode: 400,
					detail: [createErrorDetail('UNAUTHENTICATED', locale)],
				}),
				401
			);
		}
		const stub = c.get('stub');
		const payoutOrderRequest = (await c.req.json()) as PayoutOrderRequest;
		const rewardPoints = getRewardPointsFromPayoutOrder(payoutOrderRequest);
		const withdrawalAmountInUSD = getUSDValueFromPayoutOrder(payoutOrderRequest);
		// Validate payout amount
		if (rewardPoints < c.env.SMALL_MINIMUM_PAYOUT_IN_POINTS) {
			return c.json(
				formatErrorResponse({
					path: c.req.path,
					method: c.req.method,
					statusCode: 400,
					detail: [
						createErrorDetail('PAYOUT_AMOUNT_CANNOT_BE_LESS_THAN_X_POINTS', locale, {
							amount: `${c.env.SMALL_MINIMUM_PAYOUT_IN_POINTS}`,
						}),
					],
				}),
				400
			);
		}

		const subscriptionData = await stub.getSubscriptionByProductId(c.env.QUICKBUCKS_PRO_PRODUCT_ID);

		const isSubscribed = hasActiveSubscription(subscriptionData);
		const userWithdrawalProgress = await stub.getUserPayoutLimit()?.withdrawalProgress
		const allowedAmount: number = (userWithdrawalProgress?.maximumWithdrawalAmount ?? 0) - (userWithdrawalProgress?.currentWithdrawalAmount ?? 0)

		// Define withdrawal limits dynamically based on subscription status
		const allowedWithdrawalLimits: AllowedWithdrawalCount = getAllowedWithdrawalCount(
			c.env,
			isSubscribed
		);

		let currentWithdrawalLimit = await stub.getWithdrawalLimit();
		if (!currentWithdrawalLimit.success) {
			throw Error(`Failed to fetch withdrawal limits ${currentWithdrawalLimit.errorMessage}`);
		}

		// Determine withdrawal eligibility
		const isSmallWithdrawal = rewardPoints < c.env.LARGE_MINIMUM_PAYOUT_IN_POINTS;
		if (
			currentWithdrawalLimit.data.smallWithdrawalCount +
				currentWithdrawalLimit.data.largeWithdrawalCount >=
			allowedWithdrawalLimits.totalWithdrawalCount
		) {
			return c.json(
				formatErrorResponse({
					path: c.req.path,
					method: c.req.method,
					statusCode: 400,
					detail: [
						createErrorDetail('CAN_ONLY_MAKE_A_TOTAL_OF_X_WITHDRAWALS_A_MONTH', locale, {
							numWithdrawals: `${
								allowedWithdrawalLimits.largeWithdrawalCount +
								allowedWithdrawalLimits.smallWithdrawalCount
							}`,
						}),
					],
				}),
				400
			);
		}
		if (
			isSmallWithdrawal &&
			currentWithdrawalLimit.data.smallWithdrawalCount >=
				allowedWithdrawalLimits.smallWithdrawalCount
		) {
			return c.json(
				formatErrorResponse({
					path: c.req.path,
					method: c.req.method,
					statusCode: 400,
					detail: [
						createErrorDetail('CAN_MAKE_ONLY_1_WITHDRAWAL_LESS_THAN_X_POINTS_IN_A_MONTH', locale, {
							amount: `${c.env.LARGE_MINIMUM_PAYOUT_IN_POINTS}`,
						}),
					],
				}),
				400
			);
		}
		if (
			userWithdrawalProgress != null &&
			withdrawalAmountInUSD > allowedAmount
		) {
			return c.json(
				formatErrorResponse({
					path: c.req.path,
					method: c.req.method,
					statusCode: 400,
					detail: [
						createErrorDetail('CAN_ONLY_MAKE_WITHDRAWS_TO_X_AMOUNT_IN_A_MONTH', locale, {
							maximumAmount: `${c.env.WITHDRAWAL_LIMIT_NOTSUBSCRIBED_USD_AMOUNT}`,
						}),
					],
				}),
				400
			);
		}

		// Make withdrawal by calling Sunny API
		const endpoint = `${c.env.SUNNY_API_ENDPOINT}/v1/internal/payments/payout`;
		const makeWithdrawal = await fetch(endpoint, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${btoa(
					`${c.env.CLOUDFLARE_INTEGRATION_USER_USERNAME}:${c.env.CLOUDFLARE_INTEGRATION_USER_PASSWORD}`
				)}`,
				'Accept-Language': locale,
				'X-Impersonated-UserId': user.userId,
				'Content-Type': 'application/json',
				'Idempotency-Key': ulidFactory()(Date.now()),
			},
			body: JSON.stringify(payoutOrderRequest),
		});

		if (!makeWithdrawal.ok) {
			try {
				return c.json(
					(await makeWithdrawal.json()) as any,
					makeWithdrawal.status as ContentfulStatusCode
				);
			} catch (e) {
				throw Error(
					`Request to make payout on sunny failed ${
						makeWithdrawal.status
					}. Response: ${await makeWithdrawal.text()}`
				);
			}
		}

		// Update withdrawal limits
		const newWithdrawalLimit: WithdrawalLimit = {
			...currentWithdrawalLimit.data,
			accumulatedWithdrawalAmount: currentWithdrawalLimit.data.accumulatedWithdrawalAmount + withdrawalAmountInUSD,
			...(isSmallWithdrawal
				? { smallWithdrawalCount: currentWithdrawalLimit.data.smallWithdrawalCount + 1 }
				: { largeWithdrawalCount: currentWithdrawalLimit.data.largeWithdrawalCount + 1 }),
		};
		const updatedWithdrawalLimitResponse = await stub.updateWithdrawalLimit(newWithdrawalLimit);

		if (!updatedWithdrawalLimitResponse.success) {
			throw Error(
				`Failed to update withdrawal limits ${updatedWithdrawalLimitResponse.errorMessage}`
			);
		}

		// Success response
		return c.json(formatSuccessResponse({ message: 'Withdrawal success' }));
	}
);

app.get('/init', userAuthMiddleware, bindUserDurableObjectStub, async c => {
	const stub = c.get('stub');
	await stub.runMigrations();
	return c.json(null, 200);
});

app.get('/payout_limits', userAuthMiddleware, bindUserDurableObjectStub, async c => {
	const user = c.get('user');
	const locale = (c.req.header('Accept-Language') ?? 'en') as SupportedLocales;
	if (user?.userId == null) {
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				statusCode: 400,
				detail: [createErrorDetail('UNAUTHENTICATED', locale)],
			}),
			401
		);
	}
	const stub = c.get('stub');
	const payoutLimits = await stub.getUserPayoutLimit();

	return c.json(formatSuccessResponse(payoutLimits));
});

app.delete('/', userAuthMiddleware, bindUserDurableObjectStub, async c => {
	const user = c.get('user');
	const locale = (c.req.header('Accept-Language') ?? 'en') as SupportedLocales;
	const stub = c.get('stub');

	if (user?.userId == null) {
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				statusCode: 401,
				detail: [createErrorDetail('UNAUTHENTICATED', locale)],
			}),
			401
		);
	}

	const gracePeriodDays = c.env.USER_DELETION_GRACE_PERIOD_DAYS;
	const runAt = calculateHardDeletionTime(gracePeriodDays, c.env.WORKER_ENVIRONMENT);
	const userDeletionData: UserDeletionDataType = { userId: user.userId };
	const scheduledOperation: ScheduledOperationCreateType = {
		domainId: user.userId,
		runAt: runAt.toISOString(),
		status: 'pending',
		type: 'user-hard-delete',
		data: JSON.stringify(userDeletionData),
	};

	const response = await softDeleteUser(c.env, user.userId, locale);
	if (response.ok) {
		console.log('User successfully soft deleted, Scheduling an alarm for hard deletion');
		await stub.createOrUpdateScheduledOperationForUserDeletion(scheduledOperation);
	} else {
		const body = await response.json();
		console.error('Something went wrong while soft deleting user', body);
		return c.json(body as ErrorResponseType, response.status as ContentfulStatusCode);
	}

	return c.json(null, 200);
});

function calculateHardDeletionTime(gracePeriodDays: number, environment: string): Date {
	if (environment === 'dev') {
		return dayjs().add(2, 'minute').toDate();
	}
	return dayjs().add(gracePeriodDays, 'day').toDate();
}

export { app as UserHandler };
