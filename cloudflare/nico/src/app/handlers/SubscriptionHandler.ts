import { Hono } from 'hono';
import { ContentfulStatusCode } from 'hono/utils/http-status';
import { SupportedLocales } from '../localization';
import { NicoApp, ScheduledOperationCreateType } from '../../types';
import {
	bindUserDurableObjectStub,
	userAuthMiddleware,
	zValidatorWithLogging,
} from '../middlewares';
import { SubscriptionCreateRequestSchema, SubscriptionCreate } from '../../types/subscription';
import {
	calculateExpirationDate,
	calculateSubscriptionCost,
	createErrorDetail,
	ErrorResponseType,
	formatErrorResponse,
	formatSuccessResponse,
	generateULIDId,
	toSubscriptionHistory,
} from '../../utils';
import { createUserSubscription, processPayment, updateUserSubscription } from '../apis';

const app = new Hono<NicoApp>();

app.get('/', userAuthMiddleware, bindUserDurableObjectStub, async c => {
	const userStub = c.get('stub');
	const existingSubscription = await userStub.getAllUserSubscriptions();
	return c.json(formatSuccessResponse(existingSubscription));
});

app.post(
	'/',
	zValidatorWithLogging('json', SubscriptionCreateRequestSchema),
	userAuthMiddleware,
	bindUserDurableObjectStub,
	async c => {
		const locale = (c.req.header('Accept-Language') ?? 'en') as SupportedLocales;
		const idempotencyKey = c.req.header('Idempotency-Key');
		if (idempotencyKey == null) {
			return c.json(
				formatErrorResponse({
					path: c.req.path,
					method: c.req.method,
					statusCode: 400,
					detail: [createErrorDetail('IDEMPOTENCY_KEY_SHOULD_BE_PRESENT', locale)],
				}),
				400
			);
		}
		const { productId, billingCycle, paymentType } = SubscriptionCreateRequestSchema.parse(
			await c.req.json()
		);
		const user = c.get('user');

		if (user?.userId == null) {
			return c.json(
				formatErrorResponse({
					path: c.req.path,
					method: c.req.method,
					statusCode: 400,
					detail: [createErrorDetail('UNAUTHENTICATED', locale)],
				}),
				401
			);
		}

		const userStub = c.get('stub');

		// Step 1: Check Existing Subscription
		const existingSubscription = await userStub.getSubscriptionByProductId(
			c.env.QUICKBUCKS_PRO_PRODUCT_ID
		);
		if (existingSubscription) {
			return c.json(
				formatErrorResponse({
					path: c.req.path,
					method: c.req.method,
					statusCode: 400,
					detail: [createErrorDetail('SUBSCRIPTION_ALREADY_EXISTS', locale)],
				}),
				400
			);
		}

		const response = await processPayment(
			c.env,
			user.userId,
			idempotencyKey,
			{
				amount: calculateSubscriptionCost(productId, billingCycle),
				type: paymentType,
				description: 'QuickBucks Pro Subscription',
			},
			locale
		);
		if (!response.ok) {
			const errorBody = (await response.json()) as ErrorResponseType;
			console.info('Error processing payment:', errorBody);
			return c.json(errorBody, response.status as ContentfulStatusCode);
		}

		// Create Subscription
		const subscription: SubscriptionCreate = {
			subscriptionId: generateULIDId('subscription'),
			productId: c.env.QUICKBUCKS_PRO_PRODUCT_ID,
			billingCycle,
			startedAt: new Date().toISOString(),
			expiresAt: calculateExpirationDate(billingCycle, c.env.WORKER_ENVIRONMENT),
			status: 'ACTIVE',
			paymentType,
			isAutoRenewalEnabled: true,
		};

		// Step 2: Persist Subscription & Save History
		const subscriptionRecord = await userStub.createSubscription(subscription);

		// Step 3: POST subscription on user object
		try {
			await createUserSubscription(c.env, user.userId, idempotencyKey, subscriptionRecord, locale);
		} catch (error) {
			return c.json(JSON.parse((error as Error).message), 400);
		}

		// Step 4: Schedule Renewal
		const scheduledOperationPayload: ScheduledOperationCreateType = {
			type: 'renew-subscription',
			status: 'pending',
			domainId: subscription.subscriptionId,
			runAt: subscription.expiresAt,
			data: JSON.stringify({
				subscriptionId: subscription.subscriptionId,
				billingCycle: subscription.billingCycle,
			}),
		};

		await userStub.createScheduledOperationAndScheduleNextAlarm(scheduledOperationPayload);

		return c.json(formatSuccessResponse(subscriptionRecord));
	}
);

app.post('/:subscriptionId/reactivate', userAuthMiddleware, bindUserDurableObjectStub, async c => {
	const subscriptionId = c.req.param('subscriptionId');
	const locale = (c.req.header('Accept-Language') ?? 'en') as SupportedLocales;
	const user = c.get('user');
	const idempotencyKey = c.req.header('Idempotency-Key');
	if (idempotencyKey == null) {
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				statusCode: 400,
				detail: [createErrorDetail('IDEMPOTENCY_KEY_SHOULD_BE_PRESENT', locale)],
			}),
			400
		);
	}
	if (user?.userId == null) {
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				statusCode: 401,
				detail: [createErrorDetail('UNAUTHENTICATED', locale)],
			}),
			401
		);
	}
	// Update the subscription in the Durable Object
	const userStub = c.get('stub');
	const updatedSubscription = await userStub.updateSubscription(subscriptionId, {
		status: 'ACTIVE',
		isAutoRenewalEnabled: true,
	});
	c.executionCtx.waitUntil(
		userStub.saveSubscriptionHistory(
			toSubscriptionHistory(updatedSubscription, {
				currentStatus: 'ACTIVE',
				previousStatus: 'CANCELLED',
				eventType: 'ACTIVATION',
			})
		)
	);

	try {
		await updateUserSubscription(
			c.env,
			user.userId,
			// sunny accepts PATCH per productID
			updatedSubscription.productId,
			idempotencyKey,
			updatedSubscription,
			locale
		);
		return c.json(formatSuccessResponse({ message: 'Subscription reactivated successfully.' }));
	} catch (error) {
		return c.json(JSON.parse((error as Error).message), 400);
	}
});

app.delete('/:subscriptionId', userAuthMiddleware, bindUserDurableObjectStub, async c => {
	const subscriptionId = c.req.param('subscriptionId');
	const locale = (c.req.header('Accept-Language') ?? 'en') as SupportedLocales;
	const user = c.get('user');
	const idempotencyKey = c.req.header('Idempotency-Key');
	if (idempotencyKey == null) {
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				statusCode: 400,
				detail: [createErrorDetail('IDEMPOTENCY_KEY_SHOULD_BE_PRESENT', locale)],
			}),
			400
		);
	}
	if (user?.userId == null) {
		return c.json(
			formatErrorResponse({
				path: c.req.path,
				method: c.req.method,
				statusCode: 401,
				detail: [createErrorDetail('UNAUTHENTICATED', locale)],
			}),
			401
		);
	}
	// Update the subscription in the Durable Object
	const userStub = c.get('stub');
	const updatedSubscription = await userStub.updateSubscription(subscriptionId, {
		status: 'CANCELLED',
		isAutoRenewalEnabled: false,
	});
	c.executionCtx.waitUntil(
		userStub.saveSubscriptionHistory(
			toSubscriptionHistory(updatedSubscription, {
				currentStatus: 'CANCELLED',
				previousStatus: 'ACTIVE',
				eventType: 'CANCELLATION',
			})
		)
	);

	try {
		await updateUserSubscription(
			c.env,
			user.userId,
			// sunny accepts PATCH per productID
			updatedSubscription.productId,
			idempotencyKey,
			updatedSubscription,
			locale
		);
		return c.json(formatSuccessResponse({ message: 'Subscription cancelled successfully.' }));
	} catch (error) {
		return c.json(JSON.parse((error as Error).message), 400);
	}
});

export { app as SubscriptionHandler };
