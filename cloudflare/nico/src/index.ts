import { Env, <PERSON>o } from 'hono';
import { trimTrailingSlash } from 'hono/trailing-slash';
import { NicoA<PERSON> } from './types';
import { globalErrorHandler } from './utils';
import {
	InternalTaskGroupHandler,
	TaskGroupHandler,
	UserDurableObjectHandler,
	<PERSON><PERSON>sHandler,
	UserHand<PERSON>,
	SubscriptionHandler,
	ScheduledOperationHandler,
	SubscriptionHistoryHandler,
} from './app/handlers';

const app = new Hono<NicoApp>()
	.use(trimTrailingSlash())
	.route('/cf/v1/task_groups', TaskGroupHandler)
	.route('/cf/web/v1/task_groups', TaskGroupHandler)
	.route('/cf/v1/users', UserHandler)
	.route('/cf/web/v1/users', UserHandler)
	.route('/v1/subscriptions', SubscriptionHandler)
	.route('/web/v1/subscriptions', SubscriptionHandler)
	.route('/cf/v1/internal/task_groups', InternalTaskGroupHandler)
	.route('/cf/v1/internal/users', UserDurableObjectHandler)
	.route('/v1/internal/rewards', RewardsHandler)
	.route('/v1/internal/scheduled_operations', ScheduledOperationHandler)
	.route('/v1/internal/subscription_history', SubscriptionHistoryHandler)
	.onError(globalErrorHandler);

export default {
	fetch: app.fetch.bind(app),
} satisfies ExportedHandler<Env>;

export * from './durable-object';
