import { StatusCode } from 'hono/utils/http-status';
import { ErrorCode, localizationMessages, SupportedLocales } from '../app/localization';

interface ErrorDetail {
	errorCode: string;
	message: string;
}

interface ErrorResponseParams {
	path: string;
	method: string;
	detail: ErrorDetail[];
	statusCode?: number;
	type?: string;
	title?: string;
}

export type ErrorResponseType = ReturnType<typeof formatErrorResponse>;

export function createErrorDetail(
	errorCode: ErrorCode,
	lang: SupportedLocales = 'en',
	placeholders?: Record<string, string>
): ErrorDetail {
	let message = localizationMessages[lang][errorCode];
	if (placeholders) {
		for (const [key, value] of Object.entries(placeholders)) {
			message = message.replace(`{${key}}`, value);
		}
	}

	return { errorCode, message };
}

export function formatErrorResponse({
	path,
	method,
	detail,
	statusCode = 400,
	type = '',
	title = '',
}: ErrorResponseParams) {
	return {
		timestamp: new Date().toISOString(),
		path,
		method,
		type,
		status: statusCode,
		title,
		instance: path,
		detail,
	};
}

export function formatSuccessResponse(data: any, statusCode: StatusCode = 200) {
	return { success: true, data: data, statusCode };
}
