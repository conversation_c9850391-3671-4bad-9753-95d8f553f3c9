import dayjs from 'dayjs';
import { ulidFactory } from 'ulid-workers';
import { GroupContextRecordType, GroupContextType } from '../types';
import { PayoutOrderRequest } from '../types/withdrawals';

export * from './globalErrorHandler';
export * from './rpcResponseHelpers';
export * from './httpResponseHelpers';
export * from './subscriptionHelpers';
export * from './withdrawalHelpers';

export const mapToGroupDataType = (record: GroupContextRecordType): GroupContextType => {
	return {
		id: record.id,
		type: record.type,
		groupId: record.groupId,
		status: record.status,
		rewardId: record.rewardId,
		createdDate: record.createdDate,
		updatedDate: record.updatedDate,
		contextData: record.contextData ? JSON.parse(record.contextData) : {},
	};
};

const idPrefixMap = {
	'task-group-context': 'tgc',
	'rewards': 'r',
	'scheduled-operation': 'schop',
	'subscription-history': 'subshis',
	'subscription': 'sb',
};

export const generateULIDId = (objectType: keyof typeof idPrefixMap) => {
	return `${idPrefixMap[objectType]}_${ulidFactory()(Date.now())}`;
};

export const getRewardPointsFromPayoutOrder = (payoutOrderRequest: PayoutOrderRequest) => {
	switch (payoutOrderRequest.amount.currency) {
		case 'USD':
			return payoutOrderRequest.amount.value * 100;
		case 'REWARD_POINTS':
			return payoutOrderRequest.amount.value;
	}
};

export const getUSDValueFromPayoutOrder = (payoutOrderRequest: PayoutOrderRequest) => {
	switch (payoutOrderRequest.amount.currency) {
		case 'USD':
			return payoutOrderRequest.amount.value;
		case 'REWARD_POINTS':
			return payoutOrderRequest.amount.value / 100;
	}
};

export async function generateHash(input: string): Promise<string> {
	const encoder = new TextEncoder();
	const data = encoder.encode(input);
	const hashBuffer = await crypto.subtle.digest('SHA-256', data);
	const hashArray = Array.from(new Uint8Array(hashBuffer)); // Convert buffer to byte array
	const hashHex = hashArray.map(byte => byte.toString(16).padStart(2, '0')).join('');
	return hashHex;
}

export function getDateAfter90Days() {
	return dayjs().add(90, 'days').toISOString();
}

export function getDateAfterAWeek() {
	return dayjs().add(1, 'week').toISOString();
}

export function getDateAfter45Days() {
	return dayjs().add(45, 'days').toISOString();
}
