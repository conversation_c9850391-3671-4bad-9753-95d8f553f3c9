import dayjs from 'dayjs';
import {
	Subscription,
	SubscriptionCreateRequest,
	SubscriptionHistory,
	SubscriptionHistoryCreate,
} from '../types/subscription';
import { Environment } from '../types';
import { ulidFactory } from 'ulid-workers';

type SubscriptionCost = {
	MONTHLY: { value: number; currency: 'USD' };
};

const subscriptionCostInDollar: Record<string, SubscriptionCost> = {
	// Quickbucks pro
	pr_01JETVTH2X731GCAC3D0R78WB8: {
		MONTHLY: { value: 3, currency: 'USD' },
	},
};

export const calculateSubscriptionCost = (
	productId: string,
	billingCycle: SubscriptionCreateRequest['billingCycle']
) => subscriptionCostInDollar[productId][billingCycle];

/**
 * Helper to calculate subscription expiration date based on billingCycle
 */
export const calculateExpirationDate = (
	billingCycle: SubscriptionCreateRequest['billingCycle'],
	environment: Environment['WORKER_ENVIRONMENT']
): string => {
	const currentDate = dayjs();
	// To make it easy for testing in dev-
	if (environment === 'dev') {
		const nextDate = currentDate.add(2, 'minute');
		return nextDate.toISOString();
	}
	switch (billingCycle) {
		case 'MONTHLY':
			const nextDate = currentDate.add(1, 'month');
			return nextDate.toISOString();
		default:
			throw new Error('billing cycle not supported');
	}
};

export const toSubscriptionHistory = (
	subscription: Subscription,
	{
		eventType,
		previousStatus,
		currentStatus,
	}: {
		currentStatus: Subscription['status'];
		previousStatus: Subscription['status'] | null;
		eventType: 'RENEWAL' | 'CANCELLATION' | 'EXPIRATION' | 'ACTIVATION';
	}
): SubscriptionHistoryCreate => {
	return {
		productId: subscription.productId,
		subscriptionId: subscription.subscriptionId,
		eventType,
		eventTimestamp: new Date().toISOString(), // Current timestamp in ISO 8601 format
		previousStatus: previousStatus, // Can be set based on logic if tracking the previous state
		currentStatus: currentStatus,
	};
};

const generateUniqueId = (): string => {
	return ulidFactory()(Date.now());
};

const getEventType = (subscription: Subscription): string => {
	// Determine the event type based on subscription status or other logic
	switch (subscription.status) {
		case 'ACTIVE':
			return 'RENEWAL';
		case 'CANCELLED':
			return 'CANCELLATION';
		case 'EXPIRED':
			return 'EXPIRATION';
		default:
			return 'UPDATE'; // Fallback case
	}
};
