import { Context } from 'hono';
import { ZodError } from 'zod';
import { formatErrorResponse } from './httpResponseHelpers';

export function globalErrorHandler(err: Error, c: Context) {
	if (err instanceof ZodError) {
		const zodErrors = err.errors.map(e => ({
			errorCode: 'ZOD_VALIDATION_ERROR',
			message: `Path: ${e.path.toString()}. Message: ${e.message}`,
		}));
		console.error('Global Error Handler: Zod Error', err);
		return c.json(
			formatErrorResponse({
				path: c.req.routePath,
				detail: zodErrors,
				method: c.req.method,
				statusCode: 400,
				type: 'Validation Error',
				title: 'Invalid request data',
			}),
			400
		);
	}
	console.error('Global Error Handler:', err);
	return c.json(
		formatErrorResponse({
			path: c.req.routePath,
			detail: [{ errorCode: '500', message: err.message }],
			method: c.req.method,
			statusCode: 500,
			type: 'Internal Server Error',
			title: 'An unexpected error occurred',
		}),
		500
	);
}
