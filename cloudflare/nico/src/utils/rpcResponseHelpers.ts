import { StatusCode } from 'hono/utils/http-status';
import { RPCFailureResponse, RPCResponse } from '../types';
import { ErrorCode, localizationMessages, SupportedLocales } from '../app/localization';

export function successRPCResponse<T>(data: T, statusCode: StatusCode = 200): RPCResponse<T> {
	return { success: true, data, statusCode };
}

export function errorRPCResponse(
	errorCode: ErrorCode,
	statusCode: StatusCode = 500,
	lang: SupportedLocales = 'en',
	placeholders?: Record<string, string>
): RPCFailureResponse {
	let message = localizationMessages[lang][errorCode];

	// Replace any placeholders in the message (like '{status}')
	if (placeholders) {
		for (const [key, value] of Object.entries(placeholders)) {
			message = message.replace(`{${key}}`, value);
		}
	}

	return {
		success: false,
		errorMessage: message,
		errorCode,
		statusCode,
	};
}
