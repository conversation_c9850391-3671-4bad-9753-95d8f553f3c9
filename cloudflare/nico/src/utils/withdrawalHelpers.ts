import { Context } from 'hono';
import dayjs from 'dayjs';
import { NicoApp, Subscription } from '../types';
import { AllowedWithdrawalCount, PayoutLimits } from '../types/withdrawals';

export function getAllowedWithdrawalCount(
	env: Context<NicoApp>['env'],
	isSubscribed: boolean,
): AllowedWithdrawalCount {
	return isSubscribed
		? {
				smallWithdrawalCount: env.WITHDRAWAL_LIMITS_SUBSCRIBED_SMALL,
				largeWithdrawalCount: env.WITHDRAWAL_LIMITS_SUBSCRIBED_LARGE,
				totalWithdrawalCount:
					env.WITHDRAWAL_LIMITS_SUBSCRIBED_LARGE + env.WITHDRAWAL_LIMITS_SUBSCRIBED_SMALL,
		  }
		: {
				smallWithdrawalCount: env.WITHDRAWAL_LIMITS_NOTSUBSCRIBED_SMALL,
				largeWithdrawalCount: env.WITHDRAWAL_LIMITS_NOTSUBSCRIBED_LARGE,
				totalWithdrawalCount:
					env.WITHDRAWAL_LIMITS_NOTSUBSCRIBED_LARGE + env.WITHDRAWAL_LIMITS_NOTSUBSCRIBED_SMALL,
		  };
}

export function hasActiveSubscription(subscription: Subscription | null): boolean {
	if (!subscription) {
		return false;
	}
	return subscription.status !== 'EXPIRED';
}

export function isSameMonthAndYear(date1: Date, date2: Date): boolean {
	return date1.getMonth() === date2.getMonth() && date1.getFullYear() === date2.getFullYear();
}

export function getNextMonthStart(): string {
	return dayjs().add(1, 'month').startOf('month').toISOString();
}
