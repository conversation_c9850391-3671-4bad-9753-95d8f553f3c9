export const SUBSCRIPTION_TABLE = 'subscription';
export const SUBSCRIPTION_HISTORY_TABLE = 'subscription_history';

export const CREATE_SUBSCRIPTION_TABLE_QUERY = `
CREATE TABLE IF NOT EXISTS ${SUBSCRIPTION_TABLE} (
    subscriptionId TEXT PRIMARY KEY,
    productId TEXT NOT NULL,
    billingCycle TEXT NOT NULL,
    status TEXT NOT NULL,
    paymentType TEXT NOT NULL,
    startedAt DATETIME NOT NULL,
    expiresAt DATETIME NOT NULL,
    isAutoRenewalEnabled INTEGER NOT NULL DEFAULT 1,
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_subscription_expiresAt ON ${SUBSCRIPTION_TABLE} (expiresAt);
CREATE INDEX IF NOT EXISTS idx_subscription_productId ON ${SUBSCRIPTION_TABLE} (productId);
`;

export const CREATE_SUBSCRIPTION_HISTORY_TABLE_QUERY = `
CREATE TABLE IF NOT EXISTS ${SUBSCRIPTION_HISTORY_TABLE} (
    id TEXT PRIMARY KEY,
    subscriptionId TEXT NOT NULL,
    productId TEXT NOT NULL,
    eventType TEXT NOT NULL,
    eventTimestamp DATETIME NOT NULL,
    previousStatus TEXT,
    currentStatus TEXT NOT NULL,
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_subscription_history_subscriptionId ON ${SUBSCRIPTION_HISTORY_TABLE} (subscriptionId);
CREATE INDEX IF NOT EXISTS idx_subscription_history_productId ON ${SUBSCRIPTION_HISTORY_TABLE} (productId);
`;
