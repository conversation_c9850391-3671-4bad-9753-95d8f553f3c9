export const SCHEDULED_OPERATIONS_TABLE = 'scheduled_operations';

export const CREATE_SCHEDULED_OPERATIONS_TABLE_QUERY = `
CREATE TABLE IF NOT EXISTS ${SCHEDULED_OPERATIONS_TABLE} (
    id TEXT PRIMARY KEY,
    type TEXT NOT NULL, -- Type of the operation
    status TEXT NOT NULL, -- Status of the operation
    domainId TEXT NOT NULL, -- domain identifier
    runAt DATETIME NOT NULL, -- Scheduled execution time
    createdDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Creation timestamp
    updatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Last update timestamp
    data TEXT -- Serialized operation data
);
CREATE INDEX IF NOT EXISTS idx_scheduled_operations_runAt ON ${SCHEDULED_OPERATIONS_TABLE} (runAt);
`;
