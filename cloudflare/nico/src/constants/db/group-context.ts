export const GROUP_CONTEXT_TABLE = 'group_contexts';
export const ASSIGNMENT_TABLE = 'assignments';

// Column definitions for Group Context Table
export const GROUP_ID_COLUMN = 'groupId';
export const METADATA_COLUMN = 'contextData';
export const TYPE_COLUMN = 'type';
export const STATUS_COLUMN = 'status';
export const CREATED_DATE_COLUMN = 'createdDate';
export const UPDATED_DATE_COLUMN = 'updatedDate';
export const REWARD_ID_COLUMN = 'rewardId';
export const CONTEXT_DATA_COLUMN = 'contextData';

// SQL queries using the constants
export const CREATE_GROUP_CONTEXT_TABLE_QUERY = `
	CREATE TABLE IF NOT EXISTS ${GROUP_CONTEXT_TABLE} (
		id TEXT PRIMARY KEY,                           -- ULID, unique ID for the group context
		${GROUP_ID_COLUMN} TEXT NOT NULL,              -- Group ID
		${TYPE_COLUMN} TEXT,                           -- Type of group context (e.g., 5for5, 2for3, general)
		${STATUS_COLUMN} TEXT,                         -- Status of the group context (e.g., active, pending-payment, completed, expired.)
		${REWARD_ID_COLUMN} TEXT,                      
		${CREATED_DATE_COLUMN} DATETIME NOT NULL,      -- Creation timestamp
		${UPDATED_DATE_COLUMN} DATETIME NOT NULL,      -- Last update timestamp
		${CONTEXT_DATA_COLUMN} JSON BLOB               -- JSON blob for assignment IDs
	);
`;

export const CREATE_ASSIGNMENT_TABLE_QUERY = `
	CREATE TABLE IF NOT EXISTS ${ASSIGNMENT_TABLE} (
		assignmentId TEXT PRIMARY KEY,                	-- Unique assignment ID
		groupId TEXT,                                  	-- Group ID
		groupContextId TEXT,                           	-- Group context ID
		platformId TEXT NOT NULL                        -- PlatformId (required)
	);

	CREATE INDEX IF NOT EXISTS idx_groupContextId ON ${ASSIGNMENT_TABLE}(groupContextId);
`;
