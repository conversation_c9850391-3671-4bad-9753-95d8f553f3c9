
### MUTATIONS (DO NOT USE UNLESS REQUIRED)
### ------------------------------------------------------------------------------------------

### Create group context 2for3
POST https://dev-api.kazeel.com/v1/internal/task_groups/gr_01J806HC0WFDE649Q81G3MPDPJ/contexts HTTP/1.1
content-Type: application/json
Authorization: Basic ********************************************************************************
X-Impersonated-UserId: u_01JJCJ8Y65AVQJEDQ8DYNM1DK9

{"groupId":"gr_01J806HC0WFDE649Q81G3MPDPJ","type":"2for3"}

### Create group context 5for5
POST https://dev-api.kazeel.com/v1/internal/task_groups/gr_01J806HC0W5G0Z8B4XYJ7XYN9X/contexts HTTP/1.1
content-Type: application/json
Authorization: Basic ********************************************************************************
X-Impersonated-UserId: u_01JJCJ8Y65AVQJEDQ8DYNM1DK9

{"groupId":"gr_01J806HC0W5G0Z8B4XYJ7XYN9X","type":"5for5"}


### Subscription CREATE APIs
POST http://localhost:8790/v1/subscriptions HTTP/1.1
content-Type: application/json
Authorization: Basic ****************************************************************
Idempotency-Key: u_01JF72AB4JA446771Y08MCYJDM

{"productId": "pr_01JETVTH2X731GCAC3D0R78WB8", "billingCycle":"MONTHLY","paymentType":"REWARD_POINTS"}

### Subscription DELETE APIs
DELETE http://localhost:8790/v1/subscriptions/sb_01JF76FNS6JRX5GGEHAAXGGBJC HTTP/1.1
content-Type: application/json
Authorization: Basic ****************************************************************
Idempotency-Key: u_01JF72AB4JA446771Y08MCYJDMA

### ------------------------------------------------------------------------------------------


# INTERNAL OPERATION APIS
### ------------------------------------------------------------------------------------------

### Get rewards
GET https://dev-api.kazeel.com/v1/internal/rewards/ HTTP/1.1
content-Type: application/json
Authorization: Basic ********************************************************************************
X-Impersonated-UserId: u_01JJCJ8Y65AVQJEDQ8DYNM1DK9

### Get group context 2for3
GET https://dev-api.kazeel.com/v1/internal/task_groups/gr_01J806HC0WFDE649Q81G3MPDPJ/contexts HTTP/1.1
content-Type: application/json
Authorization: Basic ********************************************************************************
X-Impersonated-UserId: u_01JJCJ8Y65AVQJEDQ8DYNM1DK9

### Get group context 5for5
GET https://dev-api.kazeel.com/v1/internal/task_groups/gr_01J806HC0W5G0Z8B4XYJ7XYN9X/contexts HTTP/1.1
content-Type: application/json
Authorization: Basic ********************************************************************************
X-Impersonated-UserId: u_01JJCJ8Y65AVQJEDQ8DYNM1DK9

### Get user's demographic data
GET https://dev-api.kazeel.com/v1/outcomes/plm_01HP7XNW57XY1EYYFBVK91WJC8/ptnu_01JBCEQ96HH192T5RDJM5W9PXF HTTP/1.1
content-Type: application/json
Authorization: Basic ********************************************************************************
X-Impersonated-UserId: u_01JJCJ8Y65AVQJEDQ8DYNM1DK9

### Get all user's group contextx
GET https://dev-api.kazeel.com/v1/internal/task_groups/contexts HTTP/1.1
content-Type: application/json
Authorization: Basic ********************************************************************************
X-Impersonated-UserId: u_01JJCJ8Y65AVQJEDQ8DYNM1DK9

### Get upcoming scheduled alarm
GET https://dev-api.kazeel.com/v1/internal/users/alarm HTTP/1.1
content-Type: application/json
Authorization: Basic ********************************************************************************
X-Impersonated-UserId: u_01JJCJ8Y65AVQJEDQ8DYNM1DK9


### Internal Init DO
GET https://dev-api.kazeel.com/v1/internal/users/init HTTP/1.1
content-Type: application/json
Authorization: Basic ********************************************************************************
X-Impersonated-UserId: u_01JJCJ8Y65AVQJEDQ8DYNM1DK9


### Get all scheduled operations for a user
GET https://dev-api.kazeel.com/v1/internal/scheduled_operations HTTP/1.1
content-Type: application/json
X-Impersonated-UserId: u_01JJCJ8Y65AVQJEDQ8DYNM1DK9
Authorization: Basic ******************************************************************************

### Get subscription history for a user by productId
GET https://dev-api.kazeel.com/v1/internal/subscription_history?productId=pr_01JETVTH2X731GCAC3D0R78WB8 HTTP/1.1
content-Type: application/json
X-Impersonated-UserId: u_01JJCJ8Y65AVQJEDQ8DYNM1DK9
Authorization: Basic ******************************************************************************

### Rerun failed scheduled operations by ID
POST https://dev-api.kazeel.com/v1/internal/users/scheduled_operations/schop_01JHB5VQ70WX6XHG3F3NCY8MA7/run HTTP/1.1
content-Type: application/json
X-Impersonated-UserId: u_01JJCJ8Y65AVQJEDQ8DYNM1DK9
Authorization: Basic ******************************************************************************

### Get Withdrawal limits for user
GET https://dev-api.kazeel.com/v1/internal/users/payout_limits HTTP/1.1
content-Type: application/json
X-Impersonated-UserId: u_01JJCJ8Y65AVQJEDQ8DYNM1DK9
Authorization: Basic ******************************************************************************

### Delete all user data from DO
DELETE https://dev-api.kazeel.com/v1/internal/users HTTP/1.1
content-Type: application/json
X-Impersonated-UserId: u_01JJCJ8Y65AVQJEDQ8DYNM1DK9
Authorization: Basic ******************************************************************************

### ------------------------------------------------------------------------------------------
