name = "nico"
main = "./src/index.ts"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat_v2"]
tail_consumers = [
	{ service = "lucci" }
]

#LOCAL ENVIRONMENT
[dev]
port = 8790

[vars]
SUNNY_API_ENDPOINT = "http://localhost:8080"
WORKER_ENVIRONMENT = "local"
GROUP_CONTEXT_EXPIRY_DAYS = 30
USER_DELETION_GRACE_PERIOD_DAYS = 180

QUICKBUCKS_PRO_PRODUCT_ID = "pr_01JETVTH2X731GCAC3D0R78WB8"

# Withdrawal Limits for Subscribed Users
WITHDRAWAL_LIMITS_SUBSCRIBED_LARGE=7
WITHDRAWAL_LIMITS_SUBSCRIBED_SMALL=1

# Withdrawal Limits for Non-Subscribed Users
WITHDRAWAL_LIMITS_NOTSUBSCRIBED_LARGE=3
WITHDRAWAL_LIMITS_NOTSUBSCRIBED_SMALL=1
WITHDRAWAL_LIMIT_NOTSUBSCRIBED_USD_AMOUNT=40.0

# Minimum Payout Amount
SMALL_MINIMUM_PAYOUT_IN_DOLLARS = 1
LARGE_MINIMUM_PAYOUT_IN_DOLLARS = 5
SMALL_MINIMUM_PAYOUT_IN_POINTS = 100
LARGE_MINIMUM_PAYOUT_IN_POINTS = 500

[[queues.producers]]
binding = "EMAIL_ACTIVITY_QUEUE"
queue = "email-activity-queue-dev"

[[durable_objects.bindings]]
name = "USER_DATA_MANAGER_DURABLE_OBJECT"
class_name = "UserDataStoreManager"

[[migrations]]
tag = "v1"
new_sqlite_classes = ["UserDataStoreManager"]

#DEVELOPMENT ENVIRONMENT
[env.dev]
routes = [
	{ pattern = "dev-app.kazeel.com/cf/v1/task_groups/*", custom_domain = false },
	{ pattern = "dev-app.kazeel.com/cf/web/v1/task_groups/*", custom_domain = false },
	{ pattern = "dev-app.kazeel.com/cf/v1/users*", custom_domain = false },
	{ pattern = "dev-app.kazeel.com/cf/web/v1/users*", custom_domain = false },
	{ pattern = "dev-app.kazeel.com/v1/subscriptions*", custom_domain = false },
	{ pattern = "dev-app.kazeel.com/web/v1/subscriptions*", custom_domain = false },
	{ pattern = "dev-api.kazeel.com/cf/v1/internal/task_groups/*", custom_domain = false },
	{ pattern = "dev-api.kazeel.com/v1/internal/rewards*", custom_domain = false },
	{ pattern = "dev-api.kazeel.com/cf/v1/internal/users*", custom_domain = false },
	{ pattern = "dev-api.kazeel.com/v1/internal/subscription_history*", custom_domain = false },
	{ pattern = "dev-api.kazeel.com/v1/internal/scheduled_operations*", custom_domain = false }
]

[env.dev.observability]
enabled = true
head_sampling_rate = 1

[env.dev.vars]
SUNNY_API_ENDPOINT = "https://dev-api.kazeel.com"
WORKER_ENVIRONMENT = "dev"
QUICKBUCKS_PRO_PRODUCT_ID = "pr_01JETVTH2X731GCAC3D0R78WB8"
GROUP_CONTEXT_EXPIRY_DAYS = 30
USER_DELETION_GRACE_PERIOD_DAYS = 180

# Withdrawal Limits for Subscribed Users
WITHDRAWAL_LIMITS_SUBSCRIBED_LARGE=7
WITHDRAWAL_LIMITS_SUBSCRIBED_SMALL=1

# Withdrawal Limits for Non-Subscribed Users
WITHDRAWAL_LIMITS_NOTSUBSCRIBED_LARGE=3
WITHDRAWAL_LIMITS_NOTSUBSCRIBED_SMALL=1
WITHDRAWAL_LIMIT_NOTSUBSCRIBED_USD_AMOUNT=40.0

# Minimum Payout Amount
SMALL_MINIMUM_PAYOUT_IN_DOLLARS = 1
LARGE_MINIMUM_PAYOUT_IN_DOLLARS = 5
SMALL_MINIMUM_PAYOUT_IN_POINTS = 100
LARGE_MINIMUM_PAYOUT_IN_POINTS = 500

[[env.dev.queues.producers]]
binding = "EMAIL_ACTIVITY_QUEUE"
queue = "email-activity-queue-dev"

[[env.dev.durable_objects.bindings]]
name = "USER_DATA_MANAGER_DURABLE_OBJECT"
class_name = "UserDataStoreManager"

[[env.dev.migrations]]
tag = "v1"
new_sqlite_classes = ["UserDataStoreManager"]

[[env.dev.tail_consumers]]
service = "lucci-dev"

#PRODUCTION ENVIRONMENT
[env.prod]
routes = [
	{ pattern = "app.kazeel.com/cf/v1/task_groups/*", custom_domain = false },
	{ pattern = "app.kazeel.com/cf/v1/users*", custom_domain = false },
	{ pattern = "app.kazeel.com/cf/web/v1/users*", custom_domain = false },
	{ pattern = "app.kazeel.com/cf/web/v1/task_groups/*", custom_domain = false },
	{ pattern = "app.kazeel.com/v1/subscriptions*", custom_domain = false },
	{ pattern = "app.kazeel.com/web/v1/subscriptions*", custom_domain = false },
	{ pattern = "api.kazeel.com/cf/v1/internal/task_groups/*", custom_domain = false },
	{ pattern = "api.kazeel.com/v1/internal/rewards*", custom_domain = false },
	{ pattern = "api.kazeel.com/cf/v1/internal/users*", custom_domain = false },
	{ pattern = "api.kazeel.com/v1/internal/subscription_history*", custom_domain = false },
	{ pattern = "api.kazeel.com/v1/internal/scheduled_operations*", custom_domain = false }
]

[env.prod.observability]
enabled = true
head_sampling_rate = 1

[env.prod.vars]
SUNNY_API_ENDPOINT = "https://api.kazeel.com"
WORKER_ENVIRONMENT = "prod"
QUICKBUCKS_PRO_PRODUCT_ID = "pr_01JETVTH2X731GCAC3D0R78WB8"
GROUP_CONTEXT_EXPIRY_DAYS = 30
USER_DELETION_GRACE_PERIOD_DAYS = 180

# Withdrawal Limits for Subscribed Users
WITHDRAWAL_LIMITS_SUBSCRIBED_LARGE=7
WITHDRAWAL_LIMITS_SUBSCRIBED_SMALL=1

# Withdrawal Limits for Non-Subscribed Users
WITHDRAWAL_LIMITS_NOTSUBSCRIBED_LARGE=3
WITHDRAWAL_LIMITS_NOTSUBSCRIBED_SMALL=1
WITHDRAWAL_LIMIT_NOTSUBSCRIBED_USD_AMOUNT=40.0

# Minimum Payout Amount
SMALL_MINIMUM_PAYOUT_IN_DOLLARS = 1
LARGE_MINIMUM_PAYOUT_IN_DOLLARS = 5
SMALL_MINIMUM_PAYOUT_IN_POINTS = 100
LARGE_MINIMUM_PAYOUT_IN_POINTS = 500

[[env.prod.queues.producers]]
binding = "EMAIL_ACTIVITY_QUEUE"
queue = "email-activity-queue-prod"

[[env.prod.durable_objects.bindings]]
name = "USER_DATA_MANAGER_DURABLE_OBJECT"
class_name = "UserDataStoreManager"

[[env.prod.migrations]]
tag = "v1"
new_sqlite_classes = ["UserDataStoreManager"]

[[env.prod.tail_consumers]]
service = "lucci-prod"

#LOCAL ENVIRONMENT
[env.local]
routes = [
	{ pattern = "localhost:8790/cf/v1/task_groups/*", custom_domain = false },
	{ pattern = "localhost:8790/cf/web/v1/task_groups/*", custom_domain = false },
	{ pattern = "localhost:8790/cf/v1/users*", custom_domain = false },
	{ pattern = "localhost:8790/cf/web/v1/users*", custom_domain = false },
	{ pattern = "localhost:8790/v1/subscriptions*", custom_domain = false },
	{ pattern = "localhost:8790/web/v1/subscriptions*", custom_domain = false },
	{ pattern = "localhost:8790/cf/v1/internal/task_groups/*", custom_domain = false },
	{ pattern = "localhost:8790/v1/internal/rewards*", custom_domain = false },
	{ pattern = "localhost:8790/cf/v1/internal/users*", custom_domain = false },
	{ pattern = "localhost:8790/v1/internal/subscription_history*", custom_domain = false },
	{ pattern = "localhost:8790/v1/internal/scheduled_operations*", custom_domain = false }
]

[env.local.vars]
SUNNY_API_ENDPOINT = "http://localhost:8080"
WORKER_ENVIRONMENT = "local"
GROUP_CONTEXT_EXPIRY_DAYS = 30
USER_DELETION_GRACE_PERIOD_DAYS = 180
QUICKBUCKS_PRO_PRODUCT_ID = "pr_01JETVTH2X731GCAC3D0R78WB8"

# Withdrawal Limits for Subscribed Users
WITHDRAWAL_LIMITS_SUBSCRIBED_LARGE=7
WITHDRAWAL_LIMITS_SUBSCRIBED_SMALL=1

# Withdrawal Limits for Non-Subscribed Users
WITHDRAWAL_LIMITS_NOTSUBSCRIBED_LARGE=3
WITHDRAWAL_LIMITS_NOTSUBSCRIBED_SMALL=1
WITHDRAWAL_LIMIT_NOTSUBSCRIBED_USD_AMOUNT=40.0

# Minimum Payout Amount
SMALL_MINIMUM_PAYOUT_IN_DOLLARS = 1
LARGE_MINIMUM_PAYOUT_IN_DOLLARS = 5
SMALL_MINIMUM_PAYOUT_IN_POINTS = 100
LARGE_MINIMUM_PAYOUT_IN_POINTS = 500

[[env.local.queues.producers]]
binding = "EMAIL_ACTIVITY_QUEUE"
queue = "email-activity-queue-local"

[[env.local.durable_objects.bindings]]
name = "USER_DATA_MANAGER_DURABLE_OBJECT"
class_name = "UserDataStoreManager"

[[env.local.migrations]]
tag = "v1"
new_sqlite_classes = ["UserDataStoreManager"]

[[env.local.tail_consumers]]
service = "lucci-local"
