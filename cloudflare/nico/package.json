{"name": "nico", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "test": "vitest"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.2", "@cloudflare/workers-types": "^4.20250407.0", "typescript": "^5.7.3", "vitest": "^3.0.9", "wrangler": "4.21.2"}, "dependencies": {"@hono/zod-validator": "^0.4.2", "hono": "^4.6.20", "dayjs": "^1.11.13", "ulid-workers": "^2.1.0"}}