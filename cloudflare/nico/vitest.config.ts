import { defineWorkersConfig } from '@cloudflare/vitest-pool-workers/config';

export default defineWorkersConfig(async () => {
	return {
		test: {
			
			globals: true,
			globalSetup: ['./global-setup.ts'],
			poolOptions: {
				workers: {
					singleWorker: false,
					isolatedStorage: true,
					miniflare: {
						compatibilityDate: '2024-04-03',
						compatibilityFlags: ['nodejs_compat', 'service_binding_extra_handlers'],
						serviceBindings: {
							WORKER: 'nico',
						},
						workers: [
							{
								name: 'nico',
								modules: true,
								scriptPath: './dist/index.js',
								compatibilityDate: '2024-04-03',
								compatibilityFlags: ['nodejs_compat'],
							},
						],
					},
					wrangler: { configPath: './wrangler.toml', environment: 'local' },
				},
			},
		},
	};
});
