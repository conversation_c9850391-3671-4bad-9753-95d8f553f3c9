import { describe, expect, it, beforeEach, afterEach, vi } from 'vitest';
import { env, runInDurableObject, fetchMock } from 'cloudflare:test';
import { UserDataStoreManager } from '../src/durable-object';
import { calculateExpirationDate } from '../src/utils';
import { SubscriptionCreate } from '../src/types';
import {
	setupPayinMock,
	setupSubscriptionDELETEMock,
	setupSubscriptionPATCHMock,
	setupTransactionReleaseMock,
} from './mocks';

describe('Subscription startedAt migration test', () => {
	// Mock setup for this specific describe block
	beforeEach(() => {
		fetchMock.activate();
		setupTransactionReleaseMock();
		setupSubscriptionPATCHMock({ status: 'cancelled' }, 200);
		setupSubscriptionDELETEMock(500);
		setupPayinMock({ userId: 'u_subscriptiont1' }, 200).times(22);
	});

	afterEach(() => {
		fetchMock.deactivate();
	});

	it('should update the startedAt to the right date', async () => {
		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName(
			'test_subscription_startedAt_update_migration'
		);
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
		const now = new Date().getTime();
		const after1Month = now + 30 * 24 * 60 * 60 * 1000; // 1 month later

		// Subscription data to be created
		const subscription: SubscriptionCreate = {
			subscriptionId: 'sub_123',
			productId: env.QUICKBUCKS_PRO_PRODUCT_ID,
			billingCycle: 'MONTHLY',
			startedAt: new Date().toISOString(),
			expiresAt: calculateExpirationDate('MONTHLY', env.WORKER_ENVIRONMENT),
			status: 'ACTIVE',
			paymentType: 'REWARD_POINTS',
			isAutoRenewalEnabled: true,
		};

		// Initialize Durable Object and create subscription
		await runInDurableObject(stub, async (instance: UserDataStoreManager) => {
			await instance.storeDurableObjectName('test_subscription_startedAt_update_migration');
			await instance.createSubscription(subscription);
		});

		// Simulate time advancement to 1 month later
		vi.setSystemTime(after1Month);

		await runInDurableObject(stub, async (instance: UserDataStoreManager) => {
			await instance.storeDurableObjectName('test_subscription_startedAt_update_migration');
			await instance.subscriptionHistoryRepo.create({
				currentStatus: 'ACTIVE',
				previousStatus: 'ACTIVE',
				subscriptionId: subscription.subscriptionId,
				eventTimestamp: new Date().toISOString(),
				eventType: 'RENEWAL',
				productId: env.QUICKBUCKS_PRO_PRODUCT_ID,
			});
		});

		// Run migration
		await runInDurableObject(stub, async instance => {
			await instance.runMigrations();
		});

		// Fetch subscription after migration and assert changes
		const subscriptionAfterMigration = await runInDurableObject(stub, async instance => {
			return await instance.getSubscriptionByProductId(env.QUICKBUCKS_PRO_PRODUCT_ID);
		});

		const subscriptionHistory = await runInDurableObject(stub, async instance => {
			return await instance.getRenewalHistoryItem(env.QUICKBUCKS_PRO_PRODUCT_ID);
		});

		expect(subscriptionAfterMigration?.startedAt).toBe(subscriptionHistory!.createdAt);
	});
});
