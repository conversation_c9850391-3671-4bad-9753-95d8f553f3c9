import { describe, expect, it, beforeEach, afterEach, vi } from 'vitest';
import {
	env,
	runInDurableObject,
	runDurableObjectAlarm,
	fetchMock,
	SELF,
	listDurableObjectIds,
} from 'cloudflare:test';
import { UserDataStoreManager } from '../src/durable-object';
import { ScheduledOperationCreateType } from '../src/types';
import {
	BASIC_AUTH_KEY_ADMIN_USER,
	BASIC_AUTH_KEY_TEST_USER,
	BASIC_AUTH_KEY_TEST_USER2,
	createUser,
	setupForceDeleteMock,
	setupGetUserMock,
	setupUserSoftDeleteMock,
	setupWithdrawalMock,
	WORKER_BASE_URL,
} from './mocks';
import { getNextMonthStart } from '../src/utils';
import { initDo } from './testutils';
import { WithdrawalLimit } from '../src/types/withdrawals';

describe('handleUserDeletion', () => {
	beforeEach(() => {
		fetchMock.activate();
		createUser('testuser1', 'this is an awesome password', 'u_testuserid');
		createUser('testuser2', 'this is an awesome password', 'u_testuserid2');
		setupUserSoftDeleteMock();
	});

	afterEach(() => {
		fetchMock.deactivate();
	});

	it('should create scheduled operation for user deletion', async () => {
		const userDeletionResponse = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/users`, {
			method: 'DELETE',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER}`,
				'Content-Type': 'application/json',
			},
		});
		expect(userDeletionResponse.status).toBe(200);

		const schOperations = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/scheduled_operations`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'X-Impersonated-UserId': 'u_testuserid',
				'Content-Type': 'application/json',
			},
		});

		const { data } = (await schOperations.json()) as any;

		expect(data[0]).toEqual(
			expect.objectContaining({
				status: 'pending',
				type: 'user-hard-delete',
				isOverdue: false,
			})
		);
		// no hard delete mock set..
	});

	it('should init durable object', async () => {
		const initDOResponse = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/users/init`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER}`,
				'Content-Type': 'application/json',
			},
		});
		expect(initDOResponse.status).toBe(200);
		//@ts-ignore
		const ids = await listDurableObjectIds(env.USER_DATA_MANAGER_DURABLE_OBJECT);
		expect(ids[0].name).toBe(undefined);
		expect(ids.length).toBe(1);

		const initDOResponseTestUser2 = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/users/init`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER2}`,
				'Content-Type': 'application/json',
			},
		});
		expect(initDOResponseTestUser2.status).toBe(200);
		//@ts-ignore
		const ids2 = await listDurableObjectIds(env.USER_DATA_MANAGER_DURABLE_OBJECT);
		expect(ids2.length).toBe(2);
	});

	it('should update the alarm if user deletion is called the next time (Reactivation and again deletion case)', async () => {
		// First deletion request
		const userDeletionResponse = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/users`, {
			method: 'DELETE',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER}`,
				'Content-Type': 'application/json',
			},
		});
		expect(userDeletionResponse.status).toBe(200);

		// Fetch scheduled operations after first deletion
		const firstScheduledOperationsResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/internal/scheduled_operations`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'u_testuserid',
					'Content-Type': 'application/json',
				},
			}
		);
		expect(firstScheduledOperationsResponse.status).toBe(200);
		const { data: firstScheduledOperations } =
			(await firstScheduledOperationsResponse.json()) as any;

		// Assert the initial scheduled operation
		expect(firstScheduledOperations[0]).toEqual(
			expect.objectContaining({
				status: 'pending',
				type: 'user-hard-delete',
				isOverdue: false,
			})
		);

		const firstRunAt = new Date(firstScheduledOperations[0].runAt);

		// Simulate 15 days passing
		const dateAfter15Days = new Date();
		dateAfter15Days.setDate(dateAfter15Days.getDate() + 15);
		vi.setSystemTime(dateAfter15Days);

		// Second deletion request
		const secondDeletionResponse = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/users`, {
			method: 'DELETE',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER}`,
				'Content-Type': 'application/json',
			},
		});
		expect(secondDeletionResponse.status).toBe(200);

		// Fetch scheduled operations after second deletion
		const secondScheduledOperationsResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/internal/scheduled_operations`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'u_testuserid',
					'Content-Type': 'application/json',
				},
			}
		);
		expect(secondScheduledOperationsResponse.status).toBe(200);
		const { data: secondScheduledOperations } =
			(await secondScheduledOperationsResponse.json()) as any;

		// Assert the updated scheduled operation
		expect(secondScheduledOperations[0]).toEqual(
			expect.objectContaining({
				status: 'pending',
				type: 'user-hard-delete',
				isOverdue: false,
			})
		);

		const secondRunAt = new Date(secondScheduledOperations[0].runAt);

		// Assertions for time difference
		expect(secondRunAt.getTime()).toBeGreaterThanOrEqual(firstRunAt.getTime());

		// Additional assertion: time difference should be exactly 15 days
		const timeDifferenceInMs = secondRunAt.getTime() - firstRunAt.getTime();
		const timeDifferenceInDays = timeDifferenceInMs / (1000 * 60 * 60 * 24);
		expect(timeDifferenceInDays).toBeCloseTo(15, 1); // Allow for minor floating-point differences
	});

	it('should complete the operation if force delete succeeds', async () => {
		setupGetUserMock('u_testuserid', { userId: 'u_testuserid', isDeleted: false });
		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('u_testuserid');
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
		const runAt = new Date();
		runAt.setDate(runAt.getDate() + env.USER_DELETION_GRACE_PERIOD_DAYS);

		const scheduledOperation: ScheduledOperationCreateType = {
			domainId: 'u_testuserid',
			runAt: runAt.toISOString(),
			status: 'pending',
			type: 'user-hard-delete',
			data: JSON.stringify({ userId: 'u_testuserid' }),
		};
		await runInDurableObject(stub, async (instance: UserDataStoreManager, state) => {
			await instance.storeDurableObjectName('u_testuserid');
			expect(instance).toBeInstanceOf(UserDataStoreManager);
			await instance.createScheduledOperationAndScheduleNextAlarm(scheduledOperation);
			return await instance.getDurableObjectName();
		});

		vi.setSystemTime(runAt);

		const ran = await runDurableObjectAlarm(stub);
		expect(ran).toBe(true);

		const operations = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				await instance.storeDurableObjectName('u_testuserid');
				expect(instance).toBeInstanceOf(UserDataStoreManager);
				return await instance.getScheduledOperations();
			}
		);
		expect(operations[0].status).toBe('completed');
	});

	it('should force delete the user', async () => {
		setupGetUserMock('u_testuserid', { userId: 'u_testuserid', isDeleted: true });
		setupForceDeleteMock();
		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('u_testuserid');
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
		const runAt = new Date();
		runAt.setDate(runAt.getDate() + env.USER_DELETION_GRACE_PERIOD_DAYS);

		const scheduledOperation: ScheduledOperationCreateType = {
			domainId: 'u_testuserid',
			runAt: runAt.toISOString(),
			status: 'pending',
			type: 'user-hard-delete',
			data: JSON.stringify({ userId: 'u_testuserid' }),
		};
		await runInDurableObject(stub, async (instance: UserDataStoreManager, state) => {
			await instance.storeDurableObjectName('u_testuserid');
			expect(instance).toBeInstanceOf(UserDataStoreManager);
			await instance.createScheduledOperationAndScheduleNextAlarm(scheduledOperation);
			return await instance.getDurableObjectName();
		});

		vi.setSystemTime(runAt);

		const ran = await runDurableObjectAlarm(stub);
		expect(ran).toBe(true);

		const operations = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				await instance.storeDurableObjectName('u_testuserid');
				expect(instance).toBeInstanceOf(UserDataStoreManager);
				return await instance.getScheduledOperations();
			}
		);
		expect(operations[0].status).toBe('completed');
	});
});

describe('Withdrawal limit tests', () => {
	beforeEach(() => {
		createUser('testuser1', 'this is an awesome password', 'u_testuserid');
		createUser('testuser2', 'this is an awesome password', 'u_testuserid2');
		// setupFetchMock(BASIC_AUTH_KEY_ADMIN_USER, { userId: 'u_abcd' }, 200).times(5);
		setupWithdrawalMock({ type: 'PAYPAL', amount: { value: 1, currency: 'USD' } }, 200).times(99);
		setupWithdrawalMock({ type: 'PAYPAL', amount: { value: 10, currency: 'USD' } }, 200).times(99);
		setupWithdrawalMock({ type: 'PAYPAL', amount: { value: 4.99, currency: 'USD' } }, 200).times(
			99
		);
		setupWithdrawalMock({ type: 'PAYPAL', amount: { value: 5.01, currency: 'USD' } }, 200).times(
			99
		);
		setupWithdrawalMock({ type: 'PAYPAL', amount: { value: 2, currency: 'USD' } }, 200).persist();
		setupWithdrawalMock({ type: 'PAYPAL', amount: { value: 8, currency: 'USD' } }, 200).persist();
		setupWithdrawalMock({ type: 'PAYPAL', amount: { value: 9, currency: 'USD' } }, 200).persist();

		fetchMock.activate();
	});

	afterEach(() => {
		fetchMock.deactivate();
	});

	it('it should correctly check if the user requesting payout is the right user', async () => {
		const withdrawalResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/users/u_testuserid2/payouts`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER}`,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ type: 'PAYPAL', amount: { value: 0.5, currency: 'USD' } }),
			}
		);
		expect(withdrawalResponse.status).toBe(401);
		const responseBody = (await withdrawalResponse.json()) as any;
		expect(responseBody.detail[0].errorCode).toBe('UNAUTHENTICATED');
	});

	it('it should not let user make payout less than 1 dollar', async () => {
		const withdrawalResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/users/u_testuserid/payouts`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER}`,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ type: 'PAYPAL', amount: { value: 0.5, currency: 'USD' } }),
			}
		);
		expect(withdrawalResponse.status).toBe(400);
		const responseBody = (await withdrawalResponse.json()) as any;
		expect(responseBody.detail[0].message).toBe('Payout amount cannot be less than 100 points');
	});

	it('it should not let the user make more than 1 small withdrawal', async () => {
		await initDo(BASIC_AUTH_KEY_TEST_USER);
		const firstWithdrawal = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/users/u_testuserid/payouts`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER}`,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ type: 'PAYPAL', amount: { value: 1, currency: 'USD' } }),
			}
		);
		expect(firstWithdrawal.status).toBe(200);

		const secondSmallWithdrawal = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/users/u_testuserid/payouts`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER}`,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ type: 'PAYPAL', amount: { value: 1, currency: 'USD' } }),
			}
		);

		const secondWithdrawalResponseData = (await secondSmallWithdrawal.json()) as any;
		expect(secondSmallWithdrawal.status).toBe(400);
		const errorCode = secondWithdrawalResponseData.detail[0].errorCode;
		expect(errorCode).toBe('CAN_MAKE_ONLY_1_WITHDRAWAL_LESS_THAN_X_POINTS_IN_A_MONTH');
		const getWithdrawalLimitResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/users/payout_limits`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER}`,
					'Content-Type': 'application/json',
				},
			}
		);
		const getWithdrawalLimitResponseData = (await getWithdrawalLimitResponse.json()) as any;
		expect(getWithdrawalLimitResponseData.success).toBe(true);
		expect(getWithdrawalLimitResponseData.data.largePayoutsUsed).toBe(0);
		expect(getWithdrawalLimitResponseData.data.smallPayoutsUsed).toBe(1);

		//Third withdrawal with amount greater than 5$
		const thirdWithdrawal = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/users/u_testuserid/payouts`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER}`,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ type: 'PAYPAL', amount: { value: 10, currency: 'USD' } }),
			}
		);
		const thirdWithdrawalData = (await thirdWithdrawal.json()) as any;
		expect(thirdWithdrawalData.success).toBe(true);

		const getWithdrawalLimitResponseThird = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/users/payout_limits`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER}`,
					'Content-Type': 'application/json',
				},
			}
		);
		const getWithdrawalLimitResponseThirdData =
			(await getWithdrawalLimitResponseThird.json()) as any;
		expect(getWithdrawalLimitResponseThirdData.data.largePayoutsUsed).toBe(1);
		expect(getWithdrawalLimitResponseThirdData.data.smallPayoutsUsed).toBe(1);
	});

	it('it should not allow more than the max number of payment requests', async () => {
		// Simulate maximum withdrawal requests (e.g., 5 in this example)
		const response = await initDo(BASIC_AUTH_KEY_TEST_USER2);
		expect(response.status).toBe(200);
		const MAX_REQUESTS =
			env.WITHDRAWAL_LIMITS_NOTSUBSCRIBED_LARGE + env.WITHDRAWAL_LIMITS_NOTSUBSCRIBED_SMALL;
		for (let i = 1; i <= MAX_REQUESTS; i++) {
			const response = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/users/u_testuserid2/payouts`, {
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER2}`,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ type: 'PAYPAL', amount: { value: 10, currency: 'USD' } }),
			});
			const data = (await response.json()) as any;
			expect(data.success).toBe(true);
			expect(data).toStrictEqual({
				data: { message: 'Withdrawal success' },
				statusCode: 200,
				success: true,
			});
		}

		// Attempt to exceed max allowed payment requests
		const exceedRequest = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/users/u_testuserid2/payouts`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER2}`,
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ type: 'PAYPAL', amount: { value: 10, currency: 'USD' } }),
		});

		// Assert that the request exceeding the limit is rejected
		expect(exceedRequest.status).toBe(400);
		const exceedRequestData = (await exceedRequest.json()) as any;
		expect(exceedRequestData.detail[0].errorCode).toBe(
			'CAN_ONLY_MAKE_A_TOTAL_OF_X_WITHDRAWALS_A_MONTH'
		);

		// Validate withdrawal limits API response after the requests
		const getWithdrawalLimitResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/users/payout_limits`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER2}`,
					'Content-Type': 'application/json',
				},
			}
		);
		const getWithdrawalLimitResponseData = (await getWithdrawalLimitResponse.json()) as any;
		expect(getWithdrawalLimitResponseData.success).toBe(true);
		expect(getWithdrawalLimitResponseData.data.largePayoutsUsed).toBe(MAX_REQUESTS);
		expect(getWithdrawalLimitResponseData.data.smallPayoutsUsed).toBe(0);
	});

	it('it should migrate accumulatedWithdrawal attribute on WithdrawalLimit correctly', async () => {
		const user = 'u_1234suman23';
		const getResponse = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/internal/users/init`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': user,
			},
		});

		const responseData = (await getResponse.json()) as any;
		console.log(responseData);
		expect(responseData.success).toBe(true);

		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName(user);
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);

		await runInDurableObject(stub, async (instance: UserDataStoreManager, state) => {
			const withdrawalLimit = await instance.kvRepo.get<WithdrawalLimit>('withdrawal_limit');

			expect(withdrawalLimit?.accumulatedWithdrawalAmount).toBe(0);
		});
	});

	it('it should update the accumulated withdrawal whenever a user makes a withdrawal', async () => {
		setupGetUserMock('u_testuserid', { userId: 'u_testuserid', isDeleted: false });
		const doInitResponse = await initDo(BASIC_AUTH_KEY_TEST_USER);
		expect(doInitResponse.status).toBe(200);
		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('u_testuserid');
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);

		const firstWithdrawal = 9;
		const secondWithdrawal = 8;

		const initialWithdrawalLimit = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				return await instance.getWithdrawalLimit();
			}
		);

		expect(initialWithdrawalLimit.data?.accumulatedWithdrawalAmount).toBe(0);

		//Make the first withdrawal
		const firstResponse = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/users/u_testuserid/payouts`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER}`,
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ type: 'PAYPAL', amount: { value: firstWithdrawal, currency: 'USD' } }),
		});
		const data = (await firstResponse.json()) as any;
		expect(data.success).toBe(true);
		expect(data).toStrictEqual({
			data: { message: 'Withdrawal success' },
			statusCode: 200,
			success: true,
		});

		const afterFirstWithdrawalLimit = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				return await instance.getWithdrawalLimit();
			}
		);

		expect(afterFirstWithdrawalLimit.data?.accumulatedWithdrawalAmount).toBe(firstWithdrawal);

		//Run the second one
		const secondResponse = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/users/u_testuserid/payouts`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER}`,
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				type: 'PAYPAL',
				amount: { value: secondWithdrawal, currency: 'USD' },
			}),
		});
		const secondData = (await secondResponse.json()) as any;
		expect(secondData.success).toBe(true);
		expect(secondData).toStrictEqual({
			data: { message: 'Withdrawal success' },
			statusCode: 200,
			success: true,
		});

		const afterSecondWithdrawalLimit = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				return await instance.getWithdrawalLimit();
			}
		);

		expect(afterSecondWithdrawalLimit.data?.accumulatedWithdrawalAmount).toBe(
			firstWithdrawal + secondWithdrawal
		);
	});

	it('it should not allow unverified user make more than allowed withdrawal amount', async () => {
		const doInitResponse = await initDo(BASIC_AUTH_KEY_TEST_USER2);
		expect(doInitResponse.status).toBe(200);

		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('u_testuserid2');
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);

		const limit = env.WITHDRAWAL_LIMIT_NOTSUBSCRIBED_USD_AMOUNT;

		//Update the withdrawal limit to get it close to the limit
		await runInDurableObject(stub, async (instance: UserDataStoreManager, state) => {
			await instance.storeDurableObjectName('u_testuserid2');
			expect(instance).toBeInstanceOf(UserDataStoreManager);

			return await instance.updateWithdrawalLimit({
				largeWithdrawalCount: 0,
				smallWithdrawalCount: 0,
				accumulatedWithdrawalAmount: limit - 1,
				lastResetDate: Date.now(),
			});
		});

		//Now try to withdraw more than the allowed value
		const moreThanAllowedValue = 2;
		const exceedRequest = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/users/u_testuserid2/payouts`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER2}`,
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				type: 'PAYPAL',
				amount: { value: moreThanAllowedValue, currency: 'USD' },
			}),
		});

		// Assert that the request exceeding the limit is rejected

		expect(exceedRequest.status).toBe(400);
		const exceedRequestData = (await exceedRequest.json()) as any;
		expect(exceedRequestData.detail[0].errorCode).toBe(
			'CAN_ONLY_MAKE_WITHDRAWS_TO_X_AMOUNT_IN_A_MONTH'
		);

		expect(exceedRequestData.detail[0].message).toBe(
			`Withdrawals are limited to $${env.WITHDRAWAL_LIMIT_NOTSUBSCRIBED_USD_AMOUNT} per month for unverified users.`
		);

		// Validate withdrawal limits API response after the requests
		const getWithdrawalLimitResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/users/payout_limits`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER2}`,
					'Content-Type': 'application/json',
				},
			}
		);
		const getWithdrawalLimitResponseData = (await getWithdrawalLimitResponse.json()) as any;
		expect(getWithdrawalLimitResponseData.success).toBe(true);
		expect(getWithdrawalLimitResponseData.data.withdrawalProgress.currentWithdrawalAmount).toBe(
			limit - 1
		);
		expect(getWithdrawalLimitResponseData.data.withdrawalProgress.maximumWithdrawalAmount).toBe(
			limit
		);
	});

	it('should not reset the withdrawal limit data if alarm triggered before date', async () => {
		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('u_withdtest');
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
		const runAt = getNextMonthStart();

		const scheduledOperation: ScheduledOperationCreateType = {
			domainId: 'u_withdtest',
			runAt: runAt,
			status: 'pending',
			type: 'reset-withdrawal-data',
			data: JSON.stringify({ userId: 'u_withdtest' }),
		};
		const withdrawalData = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				await instance.storeDurableObjectName('u_withdtest');
				expect(instance).toBeInstanceOf(UserDataStoreManager);

				await instance.createScheduledOperationAndScheduleNextAlarm(scheduledOperation);
				return await instance.updateWithdrawalLimit({
					largeWithdrawalCount: 1,
					smallWithdrawalCount: 1,
					accumulatedWithdrawalAmount: 1.0,
					lastResetDate: Date.now(),
				});
			}
		);
		expect(withdrawalData.data?.largeWithdrawalCount).toBe(1);
		expect(withdrawalData.data?.smallWithdrawalCount).toBe(1);
		expect(withdrawalData.data?.accumulatedWithdrawalAmount).toBe(1.0);

		vi.setSystemTime(Date.now());

		const ran = await runDurableObjectAlarm(stub);
		expect(ran).toBe(true);

		const operations = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				await instance.storeDurableObjectName('u_withdtest');
				expect(instance).toBeInstanceOf(UserDataStoreManager);
				return await instance.getScheduledOperations();
			}
		);
		expect(operations[0].status).toBe('pending');

		const withdrawalLimit = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				return await instance.getWithdrawalLimit();
			}
		);
		expect(withdrawalLimit.data?.largeWithdrawalCount).toBe(1);
		expect(withdrawalLimit.data?.smallWithdrawalCount).toBe(1);
		expect(withdrawalLimit.data?.accumulatedWithdrawalAmount).toBe(1.0);
	});

	it('should reset the withdrawal limit data', async () => {
		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('u_withdtest');
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
		const runAt = getNextMonthStart();

		const scheduledOperation: ScheduledOperationCreateType = {
			domainId: 'u_withdtest',
			runAt: runAt,
			status: 'pending',
			type: 'reset-withdrawal-data',
			data: JSON.stringify({ userId: 'u_withdtest' }),
		};
		const withdrawalData = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				await instance.storeDurableObjectName('u_withdtest');
				expect(instance).toBeInstanceOf(UserDataStoreManager);

				await instance.createScheduledOperationAndScheduleNextAlarm(scheduledOperation);
				return await instance.updateWithdrawalLimit({
					largeWithdrawalCount: 1,
					smallWithdrawalCount: 1,
					accumulatedWithdrawalAmount: 1.0,
					lastResetDate: Date.now(),
				});
			}
		);
		expect(withdrawalData.data?.largeWithdrawalCount).toBe(1);
		expect(withdrawalData.data?.smallWithdrawalCount).toBe(1);
		expect(withdrawalData.data?.accumulatedWithdrawalAmount).toBe(1.0);

		vi.setSystemTime(runAt);

		const ran = await runDurableObjectAlarm(stub);
		expect(ran).toBe(true);

		const operations = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				await instance.storeDurableObjectName('u_withdtest');
				expect(instance).toBeInstanceOf(UserDataStoreManager);
				return await instance.getScheduledOperations();
			}
		);
		expect(operations[0].status).toBe('completed');

		const withdrawalLimit = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				return await instance.getWithdrawalLimit();
			}
		);
		expect(withdrawalLimit.data!.largeWithdrawalCount).toBe(0);
		expect(withdrawalLimit.data!.smallWithdrawalCount).toBe(0);
		expect(withdrawalLimit.data!.accumulatedWithdrawalAmount).toBe(0);
	});
});
