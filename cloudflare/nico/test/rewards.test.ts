import { SELF } from 'cloudflare:test';
import { describe, expect, it } from 'vitest';
import { BASIC_AUTH_KEY_ADMIN_USER, WORKER_BASE_URL } from './mocks';

describe('Rewards API', () => {
	it('should return error for missing authentication on POST reward', async () => {
		const resp = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/rewards`, {
			method: 'POST',
			body: JSON.stringify({ rewardId: 'reward_123' }),
		});
		expect(resp.status).toBe(401);
	});

	it('should create a reward successfully on POST', async () => {
		const validPayload = {
			name: '5 for 5 reward',
			description: 'asdfasdf',
			accumulationMode: 'single',
			currency: 'USD',
			target: 5,
			value: 5,
		};

		const postResponse = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/rewards`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': 'u_suman',
			},
			body: JSON.stringify(validPayload),
		});
		// expect(postResponse.status).toBe(201);
		const responseData = (await postResponse.json()) as any;
		expect(responseData.success).toBe(true);
		expect(responseData.data.target).toBe(validPayload.target);
		expect(responseData.data.id).toBeDefined();
	});

	it('should retrieve a reward by ID on GET', async () => {
		const validPayload = {
			name: '5 for 5 reward',
			description: 'asdfasdf',
			accumulationMode: 'single',
			currency: 'USD',
			target: 5,
			value: 5,
		};

		const postResponse = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/rewards`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': 'u_suman',
			},
			body: JSON.stringify(validPayload),
		});
		expect(postResponse.status).toBe(201);
		const { data: postData } = (await postResponse.json()) as any;

		const getResponse = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/rewards/${postData.id}`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'X-Impersonated-UserId': 'u_suman',
			},
		});
		expect(getResponse.status).toBe(200);
		const data = (await getResponse.json()) as any;
		expect(data.data).toHaveProperty('id');
	});

	it('should return 404 for non-existent reward on GET', async () => {
		const nonExistentId = 'non_existent_reward';
		const resp = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/rewards/${nonExistentId}`, {
			method: 'GET',
			headers: {
				Authorization: `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
			},
		});
		expect(resp.status).toBe(400);
		const data = await resp.json();
		expect(data).toEqual(expect.objectContaining({}));
	});

	it('should update a reward currentProgress successfully on PATCH', async () => {
		const validPayload = {
			name: '5 for 5 reward',
			description: 'asdfasdf',
			accumulationMode: 'single',
			currency: 'USD',
			target: 5,
			value: 5,
		};

		const postResponse = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/rewards`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': 'u_suman_2',
			},
			body: JSON.stringify(validPayload),
		});
		expect(postResponse.status).toBe(201);
		const { data } = (await postResponse.json()) as any;
		const updatePayload = {
			currentProgress: 1,
		};

		const patchResponse = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/rewards/${data.id}`, {
			method: 'PATCH',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': 'u_suman_2',
			},
			body: JSON.stringify(updatePayload),
		});
		expect(patchResponse.status).toBe(200);
		const responseData = (await patchResponse.json()) as any;
		expect(responseData.success).toBe(true);
		expect(responseData.data.currentProgress).toBe(updatePayload.currentProgress);
	});

	it('should increment progress by the increment value', async () => {
		const validPayload = {
			name: '5 for 5 reward',
			description: 'asdfasdf',
			accumulationMode: 'single',
			currency: 'USD',
			target: 5,
			value: 5,
		};

		const postResponse = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/rewards`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': 'u_suman_2',
			},
			body: JSON.stringify(validPayload),
		});
		expect(postResponse.status).toBe(201);
		const { data } = (await postResponse.json()) as any;
		expect(data.currentProgress).toBe(0);
		const updatePayload = {
			currentProgressChange: 2,
		};

		const updateProgress = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/internal/rewards/${data.id}/progress`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': 'u_suman_2',
				},
				body: JSON.stringify(updatePayload),
			}
		);
		expect(updateProgress.status).toBe(200);
		const responseData = (await updateProgress.json()) as any;
		expect(responseData.success).toBe(true);
		expect(responseData.data.currentProgress).toBe(2);
	});

	it('should decrement progress by the increment value if value is negative', async () => {
		const validPayload = {
			name: '5 for 5 reward',
			description: 'asdfasdf',
			accumulationMode: 'single',
			currency: 'USD',
			target: 5,
			value: 5,
		};

		const postResponse = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/rewards`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': 'u_suman_2',
			},
			body: JSON.stringify(validPayload),
		});
		expect(postResponse.status).toBe(201);
		const { data } = (await postResponse.json()) as any;
		expect(data.currentProgress).toBe(0);
		const updatePayload = {
			currentProgressChange: -1,
		};

		const updateProgress = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/internal/rewards/${data.id}/progress`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': 'u_suman_2',
				},
				body: JSON.stringify(updatePayload),
			}
		);
		expect(updateProgress.status).toBe(200);
		const responseData = (await updateProgress.json()) as any;
		expect(responseData.success).toBe(true);
		expect(responseData.data.currentProgress).toBe(-1);
	});

	it('should return an error if reward is already completed', async () => {
		const validPayload = {
			name: '5 for 5 reward',
			description: 'Already completed reward',
			accumulationMode: 'single',
			currency: 'USD',
			target: 5,
			value: 5,
		};

		const postResponse = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/rewards`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': 'u_suman_3',
			},
			body: JSON.stringify(validPayload),
		});
		expect(postResponse.status).toBe(201);

		const { data } = (await postResponse.json()) as any;
		expect(data.currentProgress).toBe(0);

		const updatePayload = {
			currentProgressChange: 5,
		};

		const updateProgress = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/internal/rewards/${data.id}/progress`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': 'u_suman_3',
				},
				body: JSON.stringify(updatePayload),
			}
		);
		expect(updateProgress.status).toBe(200);
		const responseData = (await updateProgress.json()) as any;
		expect(responseData.success).toBe(true);
		const updatePayload2 = {
			currentProgressChange: 1,
		};

		const updateProgress2 = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/internal/rewards/${data.id}/progress`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': 'u_suman_3',
				},
				body: JSON.stringify(updatePayload2),
			}
		);
		expect(updateProgress2.status).toBe(400);
	});

	it('should return an error for an invalid accumulation mode', async () => {
		const validPayload = {
			name: 'Invalid mode reward',
			description: 'Invalid accumulation mode',
			accumulationMode: 'invalid_mode',
			currency: 'USD',
			target: 5,
			value: 5,
		};

		const postResponse = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/rewards`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': 'u_suman_2',
			},
			body: JSON.stringify(validPayload),
		});
		expect(postResponse.status).toBe(400);
	});

	it('should give 400 when the new progress value exceeds the total in threshold mode', async () => {
		const validPayload = {
			name: 'Threshold reward',
			description: 'Threshold beyond target',
			accumulationMode: 'threshold',
			currency: 'USD',
			target: 10,
			value: 5,
		};

		const postResponse = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/rewards`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': 'u_suman_2',
			},
			body: JSON.stringify(validPayload),
		});
		expect(postResponse.status).toBe(201);

		const { data } = (await postResponse.json()) as any;
		expect(data.currentProgress).toBe(0); // Initial progress should be 0

		// Increment progress by 4 (first increment)
		let updatePayload = {
			currentProgressChange: 4,
		};

		let updateProgress = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/internal/rewards/${data.id}/progress`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': 'u_suman_2',
				},
				body: JSON.stringify(updatePayload),
			}
		);
		expect(updateProgress.status).toBe(200);
		let responseData = (await updateProgress.json()) as any;
		expect(responseData.success).toBe(true);
		expect(responseData.data.currentProgress).toBe(4); // Incremented by 4, now 4
		expect(responseData.data.isComplete).toBe(false); // Reward not complete yet

		// Increment progress by 7 (final increment)
		updatePayload = {
			currentProgressChange: 7,
		};

		updateProgress = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/internal/rewards/${data.id}/progress`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': 'u_suman_2',
				},
				body: JSON.stringify(updatePayload),
			}
		);
		expect(updateProgress.status).toBe(400);
	});
});
