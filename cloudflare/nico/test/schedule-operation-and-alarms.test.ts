import { describe, expect, it, beforeEach, afterEach, vi } from 'vitest';
import {
	env,
	runInDurableObject,
	runDurableObjectAlarm,
	fetchMock,
	SELF,
	listDurableObjectIds,
} from 'cloudflare:test';
import { UserDataStoreManager } from '../src/durable-object';
import { calculateExpirationDate } from '../src/utils';
import { ScheduledOperation, ScheduledOperationCreateType, SubscriptionCreate } from '../src/types';
import {
	BASIC_AUTH_KEY_ADMIN_USER,
	BASIC_AUTH_KEY_TEST_USER,
	createUser,
	setupPayinMock,
	setupSubscriptionDELETEMock,
	setupSubscriptionPATCHMock,
	setupTransactionReleaseMock,
	WORKER_BASE_URL,
} from './mocks';

describe('Alarm test', () => {
	beforeEach(() => {
		fetchMock.activate();
		setupTransactionReleaseMock();
	});

	afterEach(() => {
		fetchMock.deactivate();
	});

	describe('Scheduled Operation Tests', () => {
		beforeEach(() => {
			fetchMock.activate();
			setupTransactionReleaseMock();
			setupSubscriptionPATCHMock({ status: 'cancelled' }, 200);
			setupSubscriptionDELETEMock(500);
			setupPayinMock({ userId: 'u_subscriptiont1' }, 200).times(22);
		});

		afterEach(() => {
			fetchMock.deactivate();
		});

		it('should create a single scheduled operation and set the alarm', async () => {
			const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('test_scheduling');
			const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
			const payload: ScheduledOperationCreateType = {
				type: 'renew-subscription',
				status: 'pending',
				domainId: 'sub_123',
				runAt: new Date(new Date().getTime() + 10000).toISOString(), // 10 seconds from now
				data: JSON.stringify({ subscriptionId: 'sub_123' }),
			};

			await runInDurableObject(stub, async (instance: UserDataStoreManager) => {
				await instance.createScheduledOperationAndScheduleNextAlarm(payload);
			});

			const operations = await runInDurableObject(stub, async instance => {
				return await instance.scheduledOperationsRepo.getAll();
			});

			expect(operations).toHaveLength(1);
			expect(operations[0].type).toBe('renew-subscription');
			expect(operations[0].status).toBe('pending');
			expect(operations[0].runAt).toBe(payload.runAt);

			// Validate alarm was scheduled
			const alarm = await runInDurableObject(stub, async instance => {
				return instance.getScheduledAlarm();
			});

			expect(alarm.data?.scheduledAt?.toISOString()).toBe(operations[0].runAt);
		});

		it('should create multiple scheduled operations and set alarm for the earliest runAt', async () => {
			const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('test_scheduling_2');
			const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
			const now = new Date().getTime();

			const payloads: ScheduledOperationCreateType[] = [
				{
					type: 'renew-subscription',
					status: 'pending',
					domainId: 'sub_123',
					runAt: new Date(now + 20000).toISOString(), // 20 seconds from now
					data: JSON.stringify({ subscriptionId: 'sub_123' }),
				},
				{
					type: 'group-context-expiry',
					status: 'pending',
					domainId: 'group_456',
					runAt: new Date(now + 10000).toISOString(), // 10 seconds from now
					data: JSON.stringify({ groupContextId: 'group_456' }),
				},
			];

			await runInDurableObject(stub, async (instance: UserDataStoreManager) => {
				for (const payload of payloads) {
					await instance.createScheduledOperationAndScheduleNextAlarm(payload);
				}
			});

			const operations = await runInDurableObject(stub, async instance => {
				return await instance.scheduledOperationsRepo.getAll();
			});

			expect(operations).toHaveLength(2);

			// Validate alarm is set for the earliest runAt
			const alarmTime = await runInDurableObject(stub, async instance => {
				return instance.getScheduledAlarm();
			});

			expect(alarmTime.data!.scheduledAt?.toISOString()).toBe(payloads[1].runAt);
		});

		it('should handle concurrent scheduling of operations without race conditions', async () => {
			const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('test_scheduling_3');
			const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
			const now = new Date().getTime();

			const payloads: ScheduledOperationCreateType[] = [
				{
					type: 'renew-subscription',
					status: 'pending',
					domainId: 'sub_123',
					runAt: new Date(now + 20000).toISOString(),
					data: JSON.stringify({ subscriptionId: 'sub_123' }),
				},
				{
					type: 'group-context-expiry',
					status: 'pending',
					domainId: 'group_456',
					runAt: new Date(now + 15000).toISOString(),
					data: JSON.stringify({ groupContextId: 'group_456', groupId: '12' }),
				},
			];

			await Promise.all(
				payloads.map(payload =>
					runInDurableObject(stub, async (instance: UserDataStoreManager) => {
						await instance.createScheduledOperationAndScheduleNextAlarm(payload);
					})
				)
			);

			const operations = await runInDurableObject(stub, async instance => {
				return await instance.scheduledOperationsRepo.getAll();
			});

			expect(operations).toHaveLength(2);

			// Validate alarm is set for the earliest runAt
			const alarmTime = await runInDurableObject(stub, async instance => {
				return instance.getScheduledAlarm();
			});

			expect(alarmTime.data?.scheduledAt?.toISOString()).toBe(payloads[1].runAt);
		});

		it('should handle multiple scheduling of operations', async () => {
			const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName(
				'test_scheduling_multiple_operations'
			);
			const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
			const now = new Date().getTime();

			const groupContext = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return await instance.createGroupContext({
						groupId: 'group_567',
						type: '2for3',
						status: 'active',
					});
				}
			);

			const payloads: ScheduledOperationCreateType[] = [
				{
					type: 'renew-subscription',
					status: 'pending',
					domainId: 'sub_123',
					runAt: new Date(now + 20000).toISOString(),
					data: JSON.stringify({ subscriptionId: 'sub_123', billingCycle: 'MONTHLY' }),
				},
				{
					type: 'group-context-expiry',
					status: 'pending',
					domainId: 'group_456',
					runAt: new Date(now + 15000).toISOString(),
					data: JSON.stringify({ groupContextId: groupContext.data!.id, groupId: 'gr_12' }),
				},
			];
			const subscription: SubscriptionCreate = {
				subscriptionId: 'sub_123',
				productId: env.QUICKBUCKS_PRO_PRODUCT_ID,
				billingCycle: 'MONTHLY',
				startedAt: new Date().toISOString(),
				expiresAt: calculateExpirationDate('MONTHLY', env.WORKER_ENVIRONMENT),
				status: 'ACTIVE',
				paymentType: 'REWARD_POINTS',
				isAutoRenewalEnabled: true,
			};
			await runInDurableObject(stub, async (instance: UserDataStoreManager) => {
				await instance.storeDurableObjectName('test_scheduling_multiple_operations');
				await instance.createSubscription(subscription);
			});
			await Promise.all(
				payloads.map(payload =>
					runInDurableObject(stub, async (instance: UserDataStoreManager) => {
						await instance.createScheduledOperationAndScheduleNextAlarm(payload);
					})
				)
			);

			vi.setSystemTime(now + 15000);
			const success = await runDurableObjectAlarm(stub);
			expect(success).toBe(true);

			const operations = await runInDurableObject(stub, async instance => {
				return await instance.scheduledOperationsRepo.getAll();
			});
			expect(operations.filter(item => item.status === 'completed')).toHaveLength(1);

			vi.setSystemTime(now + 20000);
			const success2 = await runDurableObjectAlarm(stub);
			expect(success2).toBe(true);
			const operations2 = await runInDurableObject(stub, async instance => {
				return await instance.scheduledOperationsRepo.getAll();
			});

			expect(operations2.filter(item => item.status === 'completed')).toHaveLength(2);
		});

		it('should schedule alarm for the correct operation (should complete all the pending operations)', async () => {
			const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('test_correct_scheduling');
			const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
			const now = new Date().getTime();
			const groupContext = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return await instance.createGroupContext({
						groupId: 'group_567',
						type: '2for3',
						status: 'active',
					});
				}
			);
			const payloads: ScheduledOperationCreateType[] = [
				{
					type: 'renew-subscription',
					status: 'pending',
					domainId: 'sub_123',
					runAt: new Date(now + 20000).toISOString(),
					data: JSON.stringify({ subscriptionId: 'sub_123', billingCycle: 'MONTHLY' }),
				},
				{
					type: 'group-context-expiry',
					status: 'pending',
					domainId: 'group_456',
					runAt: new Date(now + 15000).toISOString(),
					data: JSON.stringify({ groupContextId: groupContext.data!.id, groupId: 'gr_12' }),
				},
			];
			const subscription: SubscriptionCreate = {
				subscriptionId: 'sub_123',
				productId: env.QUICKBUCKS_PRO_PRODUCT_ID,
				billingCycle: 'MONTHLY',
				startedAt: new Date().toISOString(),
				expiresAt: calculateExpirationDate('MONTHLY', env.WORKER_ENVIRONMENT),
				status: 'ACTIVE',
				paymentType: 'REWARD_POINTS',
				isAutoRenewalEnabled: true,
			};
			await runInDurableObject(stub, async (instance: UserDataStoreManager) => {
				await instance.storeDurableObjectName('test_correct_scheduling');
				await instance.createSubscription(subscription);
			});

			await Promise.all(
				payloads.map(payload =>
					runInDurableObject(stub, async (instance: UserDataStoreManager) => {
						await instance.createScheduledOperationAndScheduleNextAlarm(payload);
					})
				)
			);

			const alarmScheduledFor = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return instance.getScheduledAlarm();
				}
			);

			expect(alarmScheduledFor.data?.scheduledAt?.toISOString()).toBe(payloads[1].runAt);
			vi.setSystemTime(now + 20000);
			const alarmRan = await runDurableObjectAlarm(stub);
			expect(alarmRan).toBe(true);
			const operations2 = await runInDurableObject(stub, async instance => {
				return await instance.scheduledOperationsRepo.getAll();
			});

			expect(operations2.filter(item => item.status === 'completed')).toHaveLength(1);
			expect(operations2.filter(item => item.status === 'completed')[0].type).toBe(
				'group-context-expiry'
			);

			const alarmScheduledFor2 = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return instance.getScheduledAlarm();
				}
			);
			const runAt = new Date(payloads[0].runAt);
			expect(alarmScheduledFor2.data?.scheduledAt?.toISOString()).toBe(runAt.toISOString());

			//run alarm again
			const alarmRan2 = await runDurableObjectAlarm(stub);
			expect(alarmRan2).toBe(true);

			const alarmScheduledForRenewal = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return instance.getScheduledAlarm();
				}
			);
			// calculate next expiration date
			const expirationDAte = calculateExpirationDate('MONTHLY', 'local');
			//Scheduled for next month renewal..
			expect(alarmScheduledForRenewal.data?.scheduledAt?.toISOString()).toBe(expirationDAte);
		});

		it('should only attempt to complete 1 scheduled operation that is the latest', async () => {
			const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName(
				'test_scheduling_multiple_operations'
			);
			const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
			const now = new Date().getTime();
			const groupContext = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return await instance.createGroupContext({
						groupId: 'group_567',
						type: '2for3',
						status: 'active',
					});
				}
			);
			const payloads: ScheduledOperationCreateType[] = [
				{
					type: 'renew-subscription',
					status: 'pending',
					domainId: 'sub_123',
					runAt: new Date(now + 20000).toISOString(),
					data: JSON.stringify({ subscriptionId: 'sub_123', billingCycle: 'MONTHLY' }),
				},
				{
					type: 'group-context-expiry',
					status: 'pending',
					domainId: 'group_456',
					runAt: new Date(now + 15000).toISOString(),
					data: JSON.stringify({ groupContextId: groupContext.data!.id, groupId: 'gr_12' }),
				},
			];
			const subscription: SubscriptionCreate = {
				subscriptionId: 'sub_123',
				productId: env.QUICKBUCKS_PRO_PRODUCT_ID,
				billingCycle: 'MONTHLY',
				startedAt: new Date().toISOString(),
				expiresAt: calculateExpirationDate('MONTHLY', env.WORKER_ENVIRONMENT),
				status: 'ACTIVE',
				paymentType: 'REWARD_POINTS',
				isAutoRenewalEnabled: true,
			};
			await runInDurableObject(stub, async (instance: UserDataStoreManager) => {
				await instance.storeDurableObjectName('test_scheduling_multiple_operations');
				await instance.createSubscription(subscription);
			});
			await Promise.all(
				payloads.map(payload =>
					runInDurableObject(stub, async (instance: UserDataStoreManager) => {
						await instance.createScheduledOperationAndScheduleNextAlarm(payload);
					})
				)
			);

			vi.setSystemTime(now + 20000);
			const success2 = await runDurableObjectAlarm(stub);
			expect(success2).toBe(true);
			const operations2 = await runInDurableObject(stub, async instance => {
				return await instance.scheduledOperationsRepo.getAll();
			});

			expect(operations2.filter(item => item.status === 'completed')).toHaveLength(1);
			expect(
				operations2.filter(item => item.status === 'completed')[0].type == 'renew-subscription'
			);
			expect(
				operations2.filter(item => item.status === 'pending')[0].type == 'group-context-expiry'
			);
		});

		it('should handle multiple "group-context-expiry" operations correctly and in order', async () => {
			const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName(
				'test_multiple_group_context_expiry'
			);
			const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
			const now = new Date().getTime();

			const groupContext = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return await instance.createGroupContext({
						groupId: 'group_567',
						type: '2for3',
						status: 'active',
					});
				}
			);
			const groupContext2 = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return await instance.createGroupContext({
						groupId: 'group_567',
						type: '2for3',
						status: 'active',
					});
				}
			);
			const groupContext3 = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return await instance.createGroupContext({
						groupId: 'group_567',
						type: '2for3',
						status: 'active',
					});
				}
			);

			// Mock data with multiple `group-context-expiry` operations
			const payloads: ScheduledOperationCreateType[] = [
				{
					type: 'group-context-expiry',
					status: 'pending',
					domainId: 'group_001',
					runAt: new Date(now + 10000).toISOString(), // 10 seconds in the future
					data: JSON.stringify({ groupContextId: groupContext.data!.id, groupId: 'grp_001' }),
				},
				{
					type: 'group-context-expiry',
					status: 'pending',
					domainId: 'group_002',
					runAt: new Date(now + 15000).toISOString(), // 15 seconds in the future
					data: JSON.stringify({ groupContextId: groupContext2.data!.id, groupId: 'grp_002' }),
				},
				{
					type: 'group-context-expiry',
					status: 'pending',
					domainId: 'group_003',
					runAt: new Date(now + 20000).toISOString(), // 20 seconds in the future
					data: JSON.stringify({ groupContextId: groupContext3.data!.id, groupId: 'grp_003' }),
				},
			];

			runInDurableObject(stub, async (instance: UserDataStoreManager) => {
				await instance.storeDurableObjectName('test_multiple_group_context_expiry');
			});

			// Store operations in the Durable Object
			await Promise.all(
				payloads.map(payload =>
					runInDurableObject(stub, async (instance: UserDataStoreManager) => {
						await instance.createScheduledOperationAndScheduleNextAlarm(payload);
					})
				)
			);

			// Validate scheduling for the first operation
			const alarmScheduledFor = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return instance.getScheduledAlarm();
				}
			);
			expect(alarmScheduledFor.data?.scheduledAt?.toISOString()).toBe(payloads[0].runAt);

			// Simulate running the first alarm
			vi.setSystemTime(now + 10000);
			const alarmRan1 = await runDurableObjectAlarm(stub);
			expect(alarmRan1).toBe(true);

			const operationsAfterFirstRun = await runInDurableObject(stub, async instance => {
				return await instance.scheduledOperationsRepo.getAll();
			});

			// Ensure the first operation is completed
			expect(operationsAfterFirstRun.find(item => item.domainId === 'group_001')?.status).toBe(
				'completed'
			);

			// Validate scheduling for the next operation
			const alarmScheduledForNext = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return instance.getScheduledAlarm();
				}
			);
			expect(alarmScheduledForNext.data?.scheduledAt?.toISOString()).toBe(payloads[1].runAt);

			// Simulate running the second alarm
			vi.setSystemTime(now + 15000);
			const alarmRan2 = await runDurableObjectAlarm(stub);
			expect(alarmRan2).toBe(true);

			const operationsAfterSecondRun = await runInDurableObject(stub, async instance => {
				return await instance.scheduledOperationsRepo.getAll();
			});

			// Ensure the second operation is completed
			expect(operationsAfterSecondRun.find(item => item.domainId === 'group_002')?.status).toBe(
				'completed'
			);

			// Validate scheduling for the final operation
			const alarmScheduledForLast = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return instance.getScheduledAlarm();
				}
			);
			expect(alarmScheduledForLast.data?.scheduledAt?.toISOString()).toBe(payloads[2].runAt);

			// Simulate running the final alarm
			vi.setSystemTime(now + 20000);
			const alarmRan3 = await runDurableObjectAlarm(stub);
			expect(alarmRan3).toBe(true);

			const operationsAfterFinalRun = await runInDurableObject(stub, async instance => {
				return await instance.scheduledOperationsRepo.getAll();
			});

			// Ensure the final operation is completed
			expect(operationsAfterFinalRun.find(item => item.domainId === 'group_003')?.status).toBe(
				'completed'
			);
		});

		it('should handle multiple scheduled operations scheduled at the same time sequentially', async () => {
			const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName(
				'test_simultaneous_group_context_expiry_sequential'
			);
			const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
			const now = new Date().getTime();

			const groupContext = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return await instance.createGroupContext({
						groupId: 'group_567',
						type: '2for3',
						status: 'active',
					});
				}
			);
			const groupContext2 = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return await instance.createGroupContext({
						groupId: 'group_567',
						type: '2for3',
						status: 'active',
					});
				}
			);
			const groupContext3 = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return await instance.createGroupContext({
						groupId: 'group_567',
						type: '2for3',
						status: 'active',
					});
				}
			);

			// Mock data with multiple `group-context-expiry` operations scheduled at the same time
			const payloads: ScheduledOperationCreateType[] = [
				{
					type: 'group-context-expiry',
					status: 'pending',
					domainId: 'group_101',
					runAt: new Date(now + 10000).toISOString(), // 10 seconds in the future
					data: JSON.stringify({ groupContextId: groupContext.data!.id, groupId: 'grp_101' }),
				},
				{
					type: 'group-context-expiry',
					status: 'pending',
					domainId: 'group_102',
					runAt: new Date(now + 10000).toISOString(), // 10 seconds in the future
					data: JSON.stringify({ groupContextId: groupContext2.data!.id, groupId: 'grp_102' }),
				},
				{
					type: 'group-context-expiry',
					status: 'pending',
					domainId: 'group_103',
					runAt: new Date(now + 10000).toISOString(), // 10 seconds in the future
					data: JSON.stringify({ groupContextId: groupContext3.data!.id, groupId: 'grp_103' }),
				},
			];

			runInDurableObject(stub, async (instance: UserDataStoreManager) => {
				await instance.storeDurableObjectName('test_simultaneous_group_context_expiry_sequential');
			});

			// Store operations in the Durable Object
			await Promise.all(
				payloads.map(payload =>
					runInDurableObject(stub, async (instance: UserDataStoreManager) => {
						await instance.createScheduledOperationAndScheduleNextAlarm(payload);
					})
				)
			);

			// Validate scheduling for the first alarm
			let alarmScheduledFor = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return instance.getScheduledAlarm();
				}
			);
			expect(alarmScheduledFor.data?.scheduledAt?.toISOString()).toBe(payloads[0].runAt);

			// Sequentially run each alarm and validate results
			for (let i = 0; i < payloads.length; i++) {
				// Simulate time passing to the scheduled time
				vi.setSystemTime(now + 10000 + i * 100); // Add a small increment to ensure unique alarm runs

				// Run the alarm
				const alarmRan = await runDurableObjectAlarm(stub);
				expect(alarmRan).toBe(true);

				// Validate the operation's status
				const operationsAfterRun = await runInDurableObject(stub, async instance => {
					return await instance.scheduledOperationsRepo.getAll();
				});

				// Ensure the current operation is completed
				expect(
					operationsAfterRun.find(item => item.domainId === payloads[i].domainId)?.status
				).toBe('completed');

				// Validate the next alarm's scheduled time
				if (i < payloads.length - 1) {
					alarmScheduledFor = await runInDurableObject(
						stub,
						async (instance: UserDataStoreManager) => {
							return instance.getScheduledAlarm();
						}
					);

					// Alarm should now be scheduled at the current time (outdated alarms should trigger immediately)
					expect(alarmScheduledFor.data?.scheduledAt?.toISOString()).toBe(new Date().toISOString());
				}
			}
		});

		it('should not execute operations scheduled for the future', async () => {
			const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName(
				'test_future_scheduled_operations'
			);
			const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
			const now = new Date().getTime();
			const groupContext = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return await instance.createGroupContext({
						groupId: 'group_567',
						type: '2for3',
						status: 'active',
					});
				}
			);
			const groupContext2 = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return await instance.createGroupContext({
						groupId: 'group_567',
						type: '2for3',
						status: 'active',
					});
				}
			);
			const groupContext3 = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return await instance.createGroupContext({
						groupId: 'group_567',
						type: '2for3',
						status: 'active',
					});
				}
			);
			// Mock data with operations scheduled in the past, present, and future
			const payloads: ScheduledOperationCreateType[] = [
				{
					type: 'group-context-expiry',
					status: 'pending',
					domainId: 'group_past',
					runAt: new Date(now - 5000).toISOString(), // 5 seconds in the past
					data: JSON.stringify({ groupContextId: groupContext.data!.id, groupId: 'grp_past' }),
				},
				{
					type: 'group-context-expiry',
					status: 'pending',
					domainId: 'group_present',
					runAt: new Date(now).toISOString(), // Exactly now
					data: JSON.stringify({ groupContextId: groupContext2.data!.id, groupId: 'grp_present' }),
				},
				{
					type: 'group-context-expiry',
					status: 'pending',
					domainId: 'group_future',
					runAt: new Date(now + 10000).toISOString(), // 10 seconds in the future
					data: JSON.stringify({ groupContextId: groupContext3.data!.id, groupId: 'grp_future' }),
				},
			];

			await runInDurableObject(stub, async (instance: UserDataStoreManager) => {
				await instance.storeDurableObjectName('test_future_scheduled_operations');
			});

			// Store operations in the Durable Object
			await Promise.all(
				payloads.map(payload =>
					runInDurableObject(stub, async (instance: UserDataStoreManager) => {
						await instance.createScheduledOperationAndScheduleNextAlarm(payload);
					})
				)
			);

			const alarmRan = await runDurableObjectAlarm(stub);
			expect(alarmRan).toBe(true);

			// Validate operations statuses after the alarm runs
			const operationsAfterRun = await runInDurableObject(stub, async instance => {
				return await instance.scheduledOperationsRepo.getAll();
			});

			// Ensure past and present operations are completed
			expect(operationsAfterRun.find(item => item.domainId === 'group_past')?.status).toBe(
				'completed'
			);

			const alarmRan2 = await runDurableObjectAlarm(stub);
			expect(alarmRan2).toBe(true);

			// Validate operations statuses after the alarm runs
			const operationsAfterRun2 = await runInDurableObject(stub, async instance => {
				return await instance.scheduledOperationsRepo.getAll();
			});

			expect(operationsAfterRun2.find(item => item.domainId === 'group_present')?.status).toBe(
				'completed'
			);

			// Ensure future operation remains pending
			expect(operationsAfterRun.find(item => item.domainId === 'group_future')?.status).toBe(
				'pending'
			);

			const alarmRan3 = await runDurableObjectAlarm(stub);
			expect(alarmRan3).toBe(true);

			// Validate next scheduled alarm
			const alarmScheduledFor = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					return instance.getScheduledAlarm();
				}
			);
			expect(new Date(alarmScheduledFor.data!.scheduledAt!).getTime()).toBeGreaterThan(now);
		});

		// TODO
		it('should rollback created/updated items if an error occurs', async () => {
			const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('test_rollback');
			const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
			const now = new Date().getTime();

			const payload: ScheduledOperationCreateType = {
				type: 'renew-subscription',
				status: 'pending',
				domainId: 'sub_123',
				runAt: new Date(now + 20000).toISOString(),
				data: JSON.stringify({ subscriptionId: 'sub_123', billingCycle: 'MONTHLY' }),
			};
			const subscription: SubscriptionCreate = {
				subscriptionId: 'sub_123',
				productId: env.QUICKBUCKS_PRO_PRODUCT_ID,
				billingCycle: 'MONTHLY',
				startedAt: new Date().toISOString(),
				expiresAt: calculateExpirationDate('MONTHLY', env.WORKER_ENVIRONMENT),
				status: 'CANCELLED',
				paymentType: 'REWARD_POINTS',
				isAutoRenewalEnabled: true,
			};
			await runInDurableObject(stub, async (instance: UserDataStoreManager) => {
				await instance.storeDurableObjectName('test_rollback');
				await instance.createSubscription(subscription);
			});
			runInDurableObject(stub, async (instance: UserDataStoreManager) => {
				await instance.createScheduledOperationAndScheduleNextAlarm(payload);
			});

			vi.setSystemTime(now + 20000);
			let success2;
			try {
				success2 = await runDurableObjectAlarm(stub);
			} catch (error) {
				success2 = false; // Explicitly set success to false if an error occurs
			}
			expect(success2).toBe(false);
			const operations2 = await runInDurableObject(stub, async instance => {
				return await instance.scheduledOperationsRepo.getAll();
			});

			// 1 event failed because the sunny call failed --->>> setupSubscriptionDELETEMock(500);
			expect(operations2.filter(item => item.status === 'pending')).toHaveLength(1);

			const subscriptionRetained = await runInDurableObject(stub, async instance => {
				return await instance.getSubscriptionById('sub_123');
			});
			//Assert subscription deletion is rollback from nico D1
			expect(subscriptionRetained).toBeTruthy();
		});

		it('should handle alarm trigger and complete operation successfully', async () => {
			const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('test_alarm');
			const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
			const now = new Date().getTime();

			const groupContext = await runInDurableObject(
				stub,
				async (instance: UserDataStoreManager) => {
					await instance.storeDurableObjectName('test_alarm');
					return await instance.createGroupContext({
						groupId: 'group_567',
						type: '2for3',
						status: 'active',
					});
				}
			);

			const payload: ScheduledOperationCreateType = {
				type: 'group-context-expiry',
				status: 'pending',
				domainId: 'group_456',
				runAt: new Date(now + 5000).toISOString(), // 5 seconds from now
				data: JSON.stringify({ groupContextId: groupContext.data!.id, groupId: 'gr_5for5ID' }),
			};

			await runInDurableObject(stub, async (instance: UserDataStoreManager) => {
				await instance.storeDurableObjectName('test_alarm');
				await instance.createScheduledOperationAndScheduleNextAlarm(payload);
			});

			// Fast-forward time to simulate the alarm trigger
			vi.setSystemTime(now + 5000);

			const success = await runDurableObjectAlarm(stub);
			expect(success).toBe(true);

			// Validate that the operation status is updated
			const operations = await runInDurableObject(stub, async instance => {
				return await instance.scheduledOperationsRepo.getAll();
			});

			expect(operations[0].status).toBe('completed');
		});
	});

	it('should cleanup completed operations after alarm execution', async () => {
		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('test_cleanup_operations');
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
		const now = new Date().getTime();

		const subscription: SubscriptionCreate = {
			subscriptionId: 'sub_123',
			productId: env.QUICKBUCKS_PRO_PRODUCT_ID,
			billingCycle: 'MONTHLY',
			startedAt: new Date().toISOString(),
			expiresAt: calculateExpirationDate('MONTHLY', env.WORKER_ENVIRONMENT),
			status: 'CANCELLED',
			paymentType: 'REWARD_POINTS',
			isAutoRenewalEnabled: true,
		};
		const groupContext = await runInDurableObject(stub, async (instance: UserDataStoreManager) => {
			await instance.storeDurableObjectName('test_cleanup_operations');
			await instance.createSubscription(subscription);
			return await instance.createGroupContext({
				groupId: 'group_567',
				type: '2for3',
				status: 'active',
			});
		});

		const payloads: ScheduledOperationCreateType[] = [
			{
				type: 'clean-up-completed-scheduled-operations',
				status: 'pending',
				domainId: 'test_cleanup_operations',
				runAt: new Date(now + 180 * 24 * 60 * 60 * 1000).toISOString(), // 180 days in milliseconds
				data: JSON.stringify({ subscriptionId: 'test_cleanup_operations' }),
			},
			{
				type: 'renew-subscription',
				status: 'failed',
				domainId: 'sub_321',
				runAt: new Date(now).toISOString(), // 180 days in milliseconds
				data: JSON.stringify({ subscriptionId: 'sub_321' }),
			},
			{
				type: 'group-context-expiry',
				status: 'pending',
				domainId: 'group_456',
				runAt: new Date(now).toISOString(),
				data: JSON.stringify({ groupContextId: groupContext.data!.id, groupId: 'group_567' }),
			},
		];

		await runInDurableObject(stub, async (instance: UserDataStoreManager) => {
			for (const payload of payloads) {
				await instance.createScheduledOperationAndScheduleNextAlarm(payload);
			}
		});

		// Trigger alarm that runs the group context expiration operation
		await runDurableObjectAlarm(stub);
		await runDurableObjectAlarm(stub);

		// Validate that the first operation is marked as completed
		const operations = await runInDurableObject(stub, async instance => {
			return instance.scheduledOperationsRepo.getAll();
		});
		expect(operations.filter(op => op.type === 'group-context-expiry').length).toBe(1);
		expect(operations.filter(op => op.type === 'group-context-expiry')[0].status).toBe('completed');

		// Cleanup should remove completed operations that were untouched since 90 days
		vi.setSystemTime(now + 180 * 24 * 60 * 60 * 1000 + 15000);
		//this would cleanup
		await runDurableObjectAlarm(stub);

		const remainingOperations = await runInDurableObject(stub, async instance => {
			return instance.scheduledOperationsRepo.getAll();
		});
		expect(remainingOperations.length).toBe(3);
		expect(remainingOperations.filter(op => op.type == 'group-context-expiry')).toStrictEqual([]);
		//asserting did not delete failed operations
		expect(remainingOperations.filter(op => op.status == 'failed').length).toBe(1);
	});
});

describe('Scheduling alarms on initialization', () => {
	beforeEach(() => {
		fetchMock.activate();
		createUser('testuser1', 'this is an awesome password', 'u_testuser1');
	});

	afterEach(() => {
		fetchMock.deactivate();
	});

	it('should schedule operations when DO initializes', async () => {
		const initDOResponse = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/users/init`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER}`,
				'Content-Type': 'application/json',
			},
		});

		expect(initDOResponse.status).toBe(200);
		//@ts-ignore
		const ids = await listDurableObjectIds(env.USER_DATA_MANAGER_DURABLE_OBJECT);
		expect(ids[0].name).toBe(undefined);
		expect(ids.length).toBe(1);

		const schOperations = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/scheduled_operations`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'X-Impersonated-UserId': 'u_testuser1',
				'Content-Type': 'application/json',
			},
		});

		const { data }: { data: ScheduledOperation[] } = await schOperations.json();
		// Asserting correct scheduling of alarm when durable object init is called
		expect(data.length).toBe(4);
		expect(data.filter(item => item.type === 'reset-withdrawal-data')).toHaveLength(1);
		expect(data.filter(item => item.type === 'send-weekly-engagement-email')).toHaveLength(1);
		expect(
			data.filter(item => item.type === 'clean-up-completed-scheduled-operations')
		).toHaveLength(1);
		expect(data.filter(item => item.type === 'clean-up-task-opportunity-reference')).toHaveLength(
			1
		);
	});
	it('should set the startedAt attribute in subscription on renewals', async () => {
		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName(
			'test_subscription_startedAt_update_migration'
		);
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
		const now = new Date().getTime();
		const after1Month = now + 30 * 24 * 60 * 60 * 1000; // 1 month later

		// Scheduled operation payloads
		const payloads: ScheduledOperationCreateType[] = [
			{
				type: 'renew-subscription',
				status: 'pending',
				domainId: 'sub_123',
				runAt: new Date(after1Month).toISOString(), // 1 month from now
				data: JSON.stringify({ subscriptionId: 'sub_123', billingCycle: 'MONTHLY' }),
			},
		];

		// Subscription data to be created
		const subscription: SubscriptionCreate = {
			subscriptionId: 'sub_123',
			productId: env.QUICKBUCKS_PRO_PRODUCT_ID,
			billingCycle: 'MONTHLY',
			startedAt: new Date().toISOString(),
			expiresAt: calculateExpirationDate('MONTHLY', env.WORKER_ENVIRONMENT),
			status: 'ACTIVE',
			paymentType: 'REWARD_POINTS',
			isAutoRenewalEnabled: true,
		};

		// Initialize Durable Object and create subscription
		await runInDurableObject(stub, async (instance: UserDataStoreManager) => {
			await instance.storeDurableObjectName('test_subscription_startedAt_update_migration');
			await instance.createSubscription(subscription);
		});

		// Create scheduled operations
		await Promise.all(
			payloads.map(payload =>
				runInDurableObject(stub, async (instance: UserDataStoreManager) => {
					await instance.createScheduledOperationAndScheduleNextAlarm(payload);
				})
			)
		);

		// Simulate time advancement to 1 month later
		vi.setSystemTime(after1Month);

		// Run the Durable Object alarm
		const success = await runDurableObjectAlarm(stub);
		expect(success).toBe(true);

		// Verify completed operations
		const operations = await runInDurableObject(stub, async instance => {
			return await instance.scheduledOperationsRepo.getAll();
		});
		expect(operations.filter(item => item.status === 'completed')).toHaveLength(1);

		// Fetch subscription after migration and assert changes
		const subscriptionAfterRenewal = await runInDurableObject(stub, async instance => {
			return await instance.getSubscriptionByProductId(env.QUICKBUCKS_PRO_PRODUCT_ID);
		});

		expect(subscriptionAfterRenewal?.startedAt).toBe(new Date(after1Month).toISOString());
	});
});
