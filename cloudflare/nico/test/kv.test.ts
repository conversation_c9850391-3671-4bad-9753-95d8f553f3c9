import { describe, expect, it } from 'vitest';
import { env, runInDurableObject } from 'cloudflare:test';
import { UserDataStoreManager } from '../src/durable-object';

describe('KV API test', () => {
	it('should put and get durable object name with helper method storeDurableObjectName and getDurableObjectName', async () => {
		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('u_suman555');
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
		const data = await runInDurableObject(stub, async (instance: UserDataStoreManager, state) => {
			await instance.storeDurableObjectName('u_suman555');
			expect(instance).toBeInstanceOf(UserDataStoreManager);
			return await instance.getDurableObjectName();
		});
		expect(data).toBe('u_suman555');
	});

	it('should store JSON data in the KV table', async () => {
		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('u_suman5552');
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
		const data = await runInDurableObject(stub, async (instance: UserDataStoreManager, state) => {
			await instance.kvRepo.put('user', JSON.stringify({ name: 'john', class: 5 }));
			expect(instance).toBeInstanceOf(UserDataStoreManager);
			return await instance.kvRepo.get<{ name: string; class: number }>('user');
		});
		expect(data?.name).toBe('john');
		expect(data?.class).toBe(5);
	});
});
