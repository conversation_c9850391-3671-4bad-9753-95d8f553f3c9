import { ulidFactory } from 'ulid-workers';
import { describe, expect, it, beforeEach, afterEach, vi } from 'vitest';
import { env, fetchMock, SELF } from 'cloudflare:test';
import { calculateExpirationDate, generateULIDId } from '../src/utils';
import {
	BASIC_AUTH_KEY_ADMIN_USER,
	BASIC_AUTH_KEY_TEST_USER2,
	BASIC_AUTH_TEST_SUBSCRIPTION_USER,
	createUser,
	setupPayinMock,
	setupSubscriptionPATCHMock,
	setupSubscriptionPOSTMock,
	setupWithdrawalMock,
	WORKER_BASE_URL,
} from './mocks';
import { initDo } from './testutils';

describe('subscription API tests', () => {
	beforeEach(() => {
		createUser('subscriptiont1', 'this is an awesome password', 'u_subscriptiont1');
		createUser('testuser2', 'this is an awesome password', 'u_testuserid2');
		const subscriptionCreateMockPayload = {
			productId: env.QUICKBUCKS_PRO_PRODUCT_ID,
			paymentType: 'REWARD_POINTS',
			billingCycle: 'MONTHLY',
			expiresAt: calculateExpirationDate('MONTHLY', env.WORKER_ENVIRONMENT),
			isAutoRenewalEnabled: true,
			startedAt: new Date().toISOString(),
			status: 'ACTIVE',
			subscriptionId: generateULIDId('subscription'),
		};
		setupSubscriptionPOSTMock(subscriptionCreateMockPayload);
		setupSubscriptionPATCHMock({ ...subscriptionCreateMockPayload, status: 'cancelled' });
		setupWithdrawalMock({ type: 'PAYPAL', amount: { value: 1, currency: 'USD' } }, 200).times(22);
		setupWithdrawalMock({ type: 'PAYPAL', amount: { value: 10, currency: 'USD' } }, 200).times(22);
		setupWithdrawalMock({ type: 'PAYPAL', amount: { value: 4.99, currency: 'USD' } }, 200).times(
			99
		);
		setupWithdrawalMock({ type: 'PAYPAL', amount: { value: 5.01, currency: 'USD' } }, 200).times(
			99
		);
		setupPayinMock({ userId: 'u_subscriptiont1' }, 200);
		fetchMock.activate();
	});

	afterEach(() => {
		fetchMock.deactivate();
	});

	it('it should create a scheduled operation record once a subscription create occurs', async () => {
		const subscriptionResponse = await SELF.fetch(`${WORKER_BASE_URL}/v1/subscriptions`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
				'Content-Type': 'application/json',
				'Idempotency-Key': ulidFactory()(Date.now()),
				'Accept-Language': 'en',
			},
			body: JSON.stringify({
				productId: env.QUICKBUCKS_PRO_PRODUCT_ID,
				paymentType: 'REWARD_POINTS',
				billingCycle: 'MONTHLY',
			}),
		});

		expect(subscriptionResponse.status).toBe(200);

		const schOperations = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/scheduled_operations`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'X-Impersonated-UserId': 'u_subscriptiont1',
				'Content-Type': 'application/json',
			},
		});

		const { data } = (await schOperations.json()) as any;
		expect(data[0]).toEqual(
			expect.objectContaining({
				status: 'pending',
				type: 'renew-subscription',
				isOverdue: false,
			})
		);

		const subscriptions = await SELF.fetch(`${WORKER_BASE_URL}/v1/subscriptions`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
				'Content-Type': 'application/json',
			},
		});
		expect(subscriptions.status).toBe(200);
		const subscriptionData = (await subscriptions.json()) as any;
		expect(subscriptionData.data.length).toBe(1);
		expect(subscriptionData.data[0].billingCycle).toBe('MONTHLY');
		expect(subscriptionData.data[0].productId).toBe(env.QUICKBUCKS_PRO_PRODUCT_ID);
		expect(subscriptionData.data[0].status).toBe('ACTIVE');

		const subscriptionHistoryResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/internal/subscription_history?subscriptionId=${subscriptionData.data[0].subscriptionId}`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'u_subscriptiont1',
					'Content-Type': 'application/json',
				},
			}
		);
		expect(subscriptionHistoryResponse.status).toBe(200);
		const subscriptionHistoryItems = (await subscriptionHistoryResponse.json()) as any;
		expect(subscriptionHistoryItems.data.length).toBe(1);
		const historyRecord = subscriptionHistoryItems.data[0];
		expect(historyRecord.previousStatus).toBe(null);
		expect(historyRecord.currentStatus).toBe('ACTIVE');
		expect(historyRecord.eventType).toBe('ACTIVATION');
	});

	it('it should update the status of the subscription to cancelled', async () => {
		const subscriptionResponse = await SELF.fetch(`${WORKER_BASE_URL}/v1/subscriptions`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
				'Content-Type': 'application/json',
				'Idempotency-Key': ulidFactory()(Date.now()),
				'Accept-Language': 'en',
			},
			body: JSON.stringify({
				productId: env.QUICKBUCKS_PRO_PRODUCT_ID,
				paymentType: 'REWARD_POINTS',
				billingCycle: 'MONTHLY',
			}),
		});

		expect(subscriptionResponse.status).toBe(200);

		const subscriptions = await SELF.fetch(`${WORKER_BASE_URL}/v1/subscriptions`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
				'Content-Type': 'application/json',
			},
		});
		expect(subscriptions.status).toBe(200);
		const { data } = (await subscriptions.json()) as any;
		const deleteSubsResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/subscriptions/${data[0].subscriptionId}`,
			{
				method: 'DELETE',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
					'Content-Type': 'application/json',
					'Idempotency-Key': ulidFactory()(Date.now()),
					'Accept-Language': 'en',
				},
			}
		);
		expect(deleteSubsResponse.status).toBe(200);

		const subscriptionsAfterDeletion = await SELF.fetch(`${WORKER_BASE_URL}/v1/subscriptions`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
				'Content-Type': 'application/json',
			},
		});
		const deleteResponse = (await subscriptionsAfterDeletion.json()) as any;
		expect(deleteResponse.data.length).toBe(1);
		expect(deleteResponse.data[0].status).toBe('CANCELLED');

		//assert scheduled operation still present
		const schOperations = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/scheduled_operations`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'X-Impersonated-UserId': 'u_subscriptiont1',
				'Content-Type': 'application/json',
			},
		});

		const { data: schOperationData } = (await schOperations.json()) as any;
		expect(schOperationData.length).toBe(1);
		expect(schOperationData[0]).toEqual(
			expect.objectContaining({
				status: 'pending',
				type: 'renew-subscription',
				isOverdue: false,
			})
		);

		const subscriptionHistoryResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/internal/subscription_history?subscriptionId=${data[0].subscriptionId}`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'u_subscriptiont1',
					'Content-Type': 'application/json',
				},
			}
		);
		expect(subscriptionHistoryResponse.status).toBe(200);
		const subscriptionHistoryItems = (await subscriptionHistoryResponse.json()) as any;
		expect(subscriptionHistoryItems.data.length).toBe(2);
		const historyRecord = subscriptionHistoryItems.data[1];
		expect(historyRecord.previousStatus).toBe('ACTIVE');
		expect(historyRecord.currentStatus).toBe('CANCELLED');
		expect(historyRecord.eventType).toBe('CANCELLATION');
	});

	it('should reactivate a canceled subscription', async () => {
		// Step 1: Create a subscription
		const subscriptionResponse = await SELF.fetch(`${WORKER_BASE_URL}/v1/subscriptions`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
				'Content-Type': 'application/json',
				'Idempotency-Key': ulidFactory()(Date.now()),
				'Accept-Language': 'en',
			},
			body: JSON.stringify({
				productId: env.QUICKBUCKS_PRO_PRODUCT_ID,
				paymentType: 'REWARD_POINTS',
				billingCycle: 'MONTHLY',
			}),
		});

		expect(subscriptionResponse.status).toBe(200);

		// Step 2: Get the created subscription
		const subscriptions = await SELF.fetch(`${WORKER_BASE_URL}/v1/subscriptions`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
				'Content-Type': 'application/json',
			},
		});

		expect(subscriptions.status).toBe(200);
		const { data } = (await subscriptions.json()) as any;
		const subscriptionId = data[0].subscriptionId;

		// Step 3: Cancel the subscription
		const deleteSubsResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/subscriptions/${subscriptionId}`,
			{
				method: 'DELETE',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
					'Content-Type': 'application/json',
					'Idempotency-Key': ulidFactory()(Date.now()),
					'Accept-Language': 'en',
				},
			}
		);

		expect(deleteSubsResponse.status).toBe(200);

		// Step 4: Reactivate the subscription
		const reactivateResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/subscriptions/${subscriptionId}/reactivate`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
					'Content-Type': 'application/json',
					'Idempotency-Key': ulidFactory()(Date.now()),
					'Accept-Language': 'en',
				},
			}
		);

		expect(reactivateResponse.status).toBe(200);

		// Step 5: Verify the subscription is reactivated
		const subscriptionsAfterReactivation = await SELF.fetch(`${WORKER_BASE_URL}/v1/subscriptions`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
				'Content-Type': 'application/json',
			},
		});

		const reactivatedSubscriptionData = (await subscriptionsAfterReactivation.json()) as any;
		expect(reactivatedSubscriptionData.data.length).toBe(1);
		expect(reactivatedSubscriptionData.data[0].status).toBe('ACTIVE');
		expect(reactivatedSubscriptionData.data[0].isAutoRenewalEnabled).toBe(true);

		// Step 6: Verify subscription history
		const subscriptionHistoryResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/internal/subscription_history?subscriptionId=${subscriptionId}`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'u_subscriptiont1',
					'Content-Type': 'application/json',
				},
			}
		);

		expect(subscriptionHistoryResponse.status).toBe(200);
		const subscriptionHistoryItems = (await subscriptionHistoryResponse.json()) as any;
		expect(subscriptionHistoryItems.data.length).toBe(3);
		const historyRecord = subscriptionHistoryItems.data[2];
		expect(historyRecord.previousStatus).toBe('CANCELLED');
		expect(historyRecord.currentStatus).toBe('ACTIVE');
		expect(historyRecord.eventType).toBe('ACTIVATION');
	});

	it('it should allow 1 smaller payouts if the user is subscribed', async () => {
		await initDo(BASIC_AUTH_TEST_SUBSCRIPTION_USER);
		const subscriptionResponse = await SELF.fetch(`${WORKER_BASE_URL}/v1/subscriptions`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
				'Content-Type': 'application/json',
				'Idempotency-Key': ulidFactory()(Date.now()),
				'Accept-Language': 'en',
			},
			body: JSON.stringify({
				productId: env.QUICKBUCKS_PRO_PRODUCT_ID,
				paymentType: 'REWARD_POINTS',
				billingCycle: 'MONTHLY',
			}),
		});

		expect(subscriptionResponse.status).toBe(200);

		const firstWithdrawal = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/users/u_subscriptiont1/payouts`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ type: 'PAYPAL', amount: { value: 1, currency: 'USD' } }),
			}
		);
		expect(firstWithdrawal.status).toBe(200);

		const secondSmallWithdrawal = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/users/u_subscriptiont1/payouts`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ type: 'PAYPAL', amount: { value: 1, currency: 'USD' } }),
			}
		);

		const secondWithdrawalResponseData = (await secondSmallWithdrawal.json()) as any;
		expect(secondSmallWithdrawal.status).toBe(400);
	});

	it('should not allow withdrawals if the total count of small and large withdrawals reaches the limit', async () => {
		await initDo(BASIC_AUTH_TEST_SUBSCRIPTION_USER);
		// Perform 2 small withdrawals
		for (let i = 0; i < env.WITHDRAWAL_LIMITS_NOTSUBSCRIBED_SMALL; i++) {
			const smallWithdrawal = await SELF.fetch(
				`${WORKER_BASE_URL}/cf/v1/users/u_subscriptiont1/payouts`,
				{
					method: 'POST',
					headers: {
						'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({ type: 'PAYPAL', amount: { value: 1, currency: 'USD' } }),
				}
			);
			expect(smallWithdrawal.status).toBe(200);
		}

		// Perform 3 large withdrawals
		for (let i = 0; i < env.WITHDRAWAL_LIMITS_NOTSUBSCRIBED_LARGE; i++) {
			const largeWithdrawal = await SELF.fetch(
				`${WORKER_BASE_URL}/cf/v1/users/u_subscriptiont1/payouts`,
				{
					method: 'POST',
					headers: {
						'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({ type: 'PAYPAL', amount: { value: 10, currency: 'USD' } }),
				}
			);
			expect(largeWithdrawal.status).toBe(200);
		}

		// Attempt a sixth withdrawal which exceeds the total limit
		const sixthWithdrawal = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/users/u_subscriptiont1/payouts`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_TEST_SUBSCRIPTION_USER}`,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ type: 'PAYPAL', amount: { value: 1, currency: 'USD' } }),
			}
		);

		// Expect failure due to total limit exceeded
		expect(sixthWithdrawal.status).toBe(400);
		const responseData = (await sixthWithdrawal.json()) as any;
		expect(responseData.detail[0].errorCode).toBe('CAN_ONLY_MAKE_A_TOTAL_OF_X_WITHDRAWALS_A_MONTH');
	});

	it('should classify withdrawals of $5 or more as large and under $5 as small', async () => {
		await initDo(BASIC_AUTH_KEY_TEST_USER2);
		// Small withdrawal (just below $5)
		const smallWithdrawal = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/users/u_testuserid2/payouts`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER2}`,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ type: 'PAYPAL', amount: { value: 4.99, currency: 'USD' } }),
			}
		);
		expect(smallWithdrawal.status).toBe(200);

		// Large withdrawal (just above $5)
		const largeWithdrawal = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/users/u_testuserid2/payouts`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER2}`,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ type: 'PAYPAL', amount: { value: 5.01, currency: 'USD' } }),
			}
		);
		expect(largeWithdrawal.status).toBe(200);

		// Verify withdrawal limits
		const limitsResponse = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/users/payout_limits`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_TEST_USER2}`,
				'Content-Type': 'application/json',
			},
		});
		const limitsData = (await limitsResponse.json()) as any;
		expect(limitsData.data.smallPayoutsUsed).toBe(1);
		expect(limitsData.data.largePayoutsUsed).toBe(1);
	});
});
