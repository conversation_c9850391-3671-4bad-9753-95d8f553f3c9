import { SELF, env, fetchMock } from 'cloudflare:test';

export const BASIC_AUTH_KEY_TEST_USER = btoa(`testuser1:this is an awesome password`);
export const BASIC_AUTH_TEST_SUBSCRIPTION_USER = btoa(`subscriptiont1:this is an awesome password`);
export const BASIC_AUTH_KEY_TEST_USER2 = btoa(`testuser2:this is an awesome password`);

export const WORKER_BASE_URL = 'http://localhost:8790';
export const BASIC_AUTH_KEY_ADMIN_USER = btoa(
	`${env.CLOUDFLARE_INTEGRATION_USER_USERNAME}:${env.CLOUDFLARE_INTEGRATION_USER_PASSWORD}`
);

export const createUser = (username: string, password: string, userId: string) => {
	const basicToken = btoa(`${username}:${password}`);
	setupUserFetch(basicToken, { userId });
	return basicToken;
};

export const setupUserFetch = (requestedUserAuthKey: string, response: any, status = 200) => {
	const pingUserPath = `web/v1/users`;
	return fetchMock
		.get(env.SUNNY_API_ENDPOINT)
		.intercept({
			path: pingUserPath,
			headers: {
				Authorization: `Basic ${requestedUserAuthKey}`,
			},
			method: 'get',
		})
		.reply(status, response)
		.persist();
};

export const setupGetUserMock = (userId: string, response: any, status = 200) => {
	const getUserPath = `v1/internal/users`;
	return fetchMock
		.get(env.SUNNY_API_ENDPOINT)
		.intercept({
			path: getUserPath,
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'X-Impersonated-UserId': userId,
				'content-type': 'application/json',
			},
			method: 'get',
		})
		.reply(status, response);
};

export const setupWithdrawalMock = (data: object, status = 200) => {
	const makewithdrawalEndPoint = `v1/internal/payments/payout`;
	return fetchMock
		.get(env.SUNNY_API_ENDPOINT)
		.intercept({
			path: makewithdrawalEndPoint,
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(data),
			method: 'post',
		})
		.reply(status, {});
};

export const setupSubscriptionPOSTMock = (responseData: object, status = 200) => {
	const createSubscriptionPath = `v1/internal/subscriptions`;
	return fetchMock
		.get(env.SUNNY_API_ENDPOINT)
		.intercept({
			path: createSubscriptionPath,
			method: 'post',
		})
		.reply(status, responseData)
		.persist();
};

export const setupSubscriptionPATCHMock = (response: object, status = 200) => {
	const subscriptionPUTPath = /v1\/internal\/subscriptions\/.*$/;
	return fetchMock
		.get(env.SUNNY_API_ENDPOINT)
		.intercept({
			path: subscriptionPUTPath,
			method: 'PATCH',
		})
		.reply(status, response)
		.persist();
};

export const setupSubscriptionDELETEMock = (status = 200) => {
	const subscriptionPUTPath = /v1\/internal\/subscriptions\/.*$/;
	return fetchMock
		.get(env.SUNNY_API_ENDPOINT)
		.intercept({
			path: subscriptionPUTPath,
			method: 'DELETE',
		})
		.reply(status, {})
		.persist();
};

export const setupPayinMock = (payload: { userId: string }, status = 200) => {
	const payinPath = `v1/internal/payments/payin`;
	return fetchMock
		.get(env.SUNNY_API_ENDPOINT)
		.intercept({
			path: payinPath,
			method: 'post',
		})
		.reply(status, {});
};

export const setupTransactionReleaseMock = (status = 200) => {
	const transactionReleaseEndpoint = `v1/internal/webhooks/transactions/release`;
	fetchMock
		.get(env.SUNNY_API_ENDPOINT)
		.intercept({
			path: transactionReleaseEndpoint,
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
			},
			method: 'post',
		})
		.reply(status, {})
		.persist();
	fetchMock.disableNetConnect();
};

export const setupCleanupTaskOpportunityReferencesMock = (status = 200) => {
	const taskOpportunityReferencesCleanupPath = `v1/internal/users/task_opportunity_references/cleanup`;
	fetchMock
		.get(env.SUNNY_API_ENDPOINT)
		.intercept({
			path: taskOpportunityReferencesCleanupPath,
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
			},
			method: 'post',
		})
		.reply(status, {})
		.persist();
	fetchMock.disableNetConnect();
};

export const setupUserSoftDeleteMock = (status = 200) => {
	const userDeactivatePath = `v1/internal/users/soft`;
	fetchMock
		.get(env.SUNNY_API_ENDPOINT)
		.intercept({
			path: userDeactivatePath,
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
			},
			method: 'delete',
		})
		.reply(status, {})
		.times(4);
	fetchMock.disableNetConnect();
};

export const setupForceDeleteMock = (status = 200) => {
	const userDeleteForcePath = `v1/internal/users/hard`;
	fetchMock
		.get(env.SUNNY_API_ENDPOINT)
		.intercept({
			path: userDeleteForcePath,
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
			},
			method: 'delete',
		})
		.reply(status, {})
		.persist();
	fetchMock.disableNetConnect();
};

export const createGroupContext = async (
	userId: string,
	payload: { groupId: string; type: string }
) => {
	const postResponse = await SELF.fetch(
		`${WORKER_BASE_URL}/cf/v1/internal/task_groups/${payload.groupId}/contexts`,
		{
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': userId,
			},
			body: JSON.stringify(payload),
		}
	);

	return postResponse;
};
