import { SELF, env, runDurableObjectAlarm, runInDurableObject } from 'cloudflare:test';
import { describe, expect, it, vi } from 'vitest';
import { UserDataStoreManager } from '../src/durable-object';
import { ScheduledOperationCreateType } from '../src/types';
import { getDateAfterAWeek } from '../src/utils';
import { BASIC_AUTH_KEY_ADMIN_USER, WORKER_BASE_URL } from './mocks';

describe('User durable object API test (/v1/internal/users)', () => {
	it('should return 401 when the authentication token is not valid', async () => {
		const resp = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/internal/users`, {
			method: 'DELETE',
			headers: {
				Authorization: `Basic ${BASIC_AUTH_KEY_ADMIN_USER}invalid`,
			},
		});
		expect(resp.status).toBe(401);
	});

	it('should remove all data from durable object with /cf/v1/internal/users', async () => {
		const rewardPayload = {
			name: '5 for 5 reward',
			description: 'asdfasdf',
			accumulationMode: 'single',
			currency: 'USD',
			target: 5,
			value: 5,
		};

		// create a reward
		const postResponse = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/rewards`, {
			method: 'POST',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': 'u_1234suman22',
			},
			body: JSON.stringify(rewardPayload),
		});

		const responseData = (await postResponse.json()) as any;
		expect(responseData.success).toBe(true);
		expect(responseData.data.target).toBe(rewardPayload.target);
		expect(responseData.data.id).toBeDefined();

		// get reward with id
		const getResp = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/internal/rewards/${responseData.data.id}`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'u_1234suman22',
				},
			}
		);
		// assert presence of reward
		expect(getResp.status).toBe(200);

		// delete durable_object data
		const deleteDurableObject = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/internal/users`, {
			method: 'DELETE',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': 'u_1234suman22',
			},
		});
		expect(deleteDurableObject.status).toBe(204);

		// get durable_object data
		const resp = await SELF.fetch(
			`${WORKER_BASE_URL}/v1/internal/rewards/${responseData.data.id}`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'u_1234suman22',
				},
			}
		);
		// verify durable_object data not present
		expect(resp.status).toBe(404);
	});

	it('should return an error for missing authorization header', async () => {
		const postResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/users/demographic_survey_completion`,
			{
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': 'u_1234suman22',
				},
			}
		);

		expect(postResponse.status).toBe(401);
		const responseData = await postResponse.json();
		expect(responseData).toEqual(
			expect.objectContaining({
				message: 'Authentication failed',
			})
		);
	});

	it('should schedule operation for sending a weekly engagement email', async () => {
		const getResponse = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/internal/users/init`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'Content-Type': 'application/json',
				'X-Impersonated-UserId': 'u_1234suman22',
			},
		});

		const responseData = (await getResponse.json()) as any;
		expect(responseData.success).toBe(true);

		const schOperations = await SELF.fetch(`${WORKER_BASE_URL}/v1/internal/scheduled_operations`, {
			method: 'GET',
			headers: {
				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				'X-Impersonated-UserId': 'u_1234suman22',
				'Content-Type': 'application/json',
			},
		});

		const { data } = (await schOperations.json()) as any;
		const scheduledOperation = data.filter(
			(item: any) => item.type === 'send-weekly-engagement-email'
		)[0];

		expect(scheduledOperation).toEqual(
			expect.objectContaining({
				status: 'pending',
				type: 'send-weekly-engagement-email',
				isOverdue: false,
			})
		);

		// Calculate expected runAt date
		const now = new Date();
		const expectedRunAt = new Date(now);
		expectedRunAt.setDate(now.getDate() + 7);
		const expectedRunAtISO = expectedRunAt.toISOString().slice(0, 10); // Only compare the date part

		// Check if the runAt date is correct
		const actualRunAtDate = new Date(scheduledOperation.runAt).toISOString().slice(0, 10);
		expect(actualRunAtDate).toBe(expectedRunAtISO);
	});
});

describe('testing send-weekly-engagement-email', () => {
	it('should send email message to the queue and mark operation completed', async () => {
		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('u_testuserid');
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);

		// Mock EMAIL_ACTIVITY_QUEUE
		const sendMock = vi.fn();
		env.EMAIL_ACTIVITY_QUEUE.send = sendMock;

		const scheduledOperations = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				await instance.storeDurableObjectName('u_testuserid');
				expect(instance).toBeInstanceOf(UserDataStoreManager);
				await instance.runMigrations();
				return await instance.getScheduledOperations();
			}
		);

		const weeklyEmailOperations = scheduledOperations.filter(
			item => item.type == 'send-weekly-engagement-email'
		);

		expect(weeklyEmailOperations).toHaveLength(1);

		// Simulate the system time when the alarm should run
		vi.setSystemTime(weeklyEmailOperations[0].runAt);

		// Trigger the Durable Object alarm 4 times.. as there are 4 operations that would be scheduled and pending
		await runDurableObjectAlarm(stub);
		await runDurableObjectAlarm(stub);
		await runDurableObjectAlarm(stub);
		await runDurableObjectAlarm(stub);

		const operations = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				return await instance.getScheduledOperations();
			}
		);
		const weeklyEmailOperations2 = operations.filter(
			item => item.type == 'send-weekly-engagement-email'
		);
		expect(weeklyEmailOperations2).toHaveLength(2);
		expect(weeklyEmailOperations2.find(item => item.status === 'completed')).toBeDefined();

		// Validate that the email message was sent to the queue
		expect(sendMock).toHaveBeenCalledTimes(1);
		expect(sendMock).toHaveBeenCalledWith({
			userId: 'u_testuserid',
			templateName: 'WEEKLY_EARNINGS',
		});
		// assert new weekly email operation has been created
		expect(weeklyEmailOperations2.find(item => item.status === 'pending')).toBeDefined();
	});

	it('should send email message to the queue and mark operation completed and schedule a new alarm', async () => {
		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('u_testuserid');
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
		const runAt = getDateAfterAWeek();

		// Mock EMAIL_ACTIVITY_QUEUE
		const sendMock = vi.fn();
		env.EMAIL_ACTIVITY_QUEUE.send = sendMock;

		const scheduledOperation: ScheduledOperationCreateType = {
			domainId: 'u_testuserid',
			runAt: runAt,
			status: 'pending',
			type: 'send-weekly-engagement-email',
		};

		await runInDurableObject(stub, async (instance: UserDataStoreManager, state) => {
			await instance.storeDurableObjectName('u_testuserid');
			expect(instance).toBeInstanceOf(UserDataStoreManager);
			await instance.createScheduledOperationAndScheduleNextAlarm(scheduledOperation);
			return await instance.getDurableObjectName();
		});

		// Simulate the system time when the alarm should run
		vi.setSystemTime(runAt);

		// Trigger the Durable Object alarm
		const ran = await runDurableObjectAlarm(stub);
		expect(ran).toBe(true);

		const operations = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				await instance.storeDurableObjectName('u_testuserid');
				expect(instance).toBeInstanceOf(UserDataStoreManager);
				return await instance.getScheduledOperations();
			}
		);

		expect(operations.length).toBe(2);

		expect(operations.find(operation => operation.status === 'completed')).toBeDefined();
		expect(operations.find(operation => operation.status === 'pending')).toBeDefined();
		const today = new Date();
		const sevenDaysLater = new Date(today);
		sevenDaysLater.setDate(today.getDate() + 7);

		// Assuming `operations` is the array containing your operations
		const newRunAt = operations.find(operation => operation.status === 'pending')?.runAt;

		if (newRunAt) {
			const runAtDate = new Date(newRunAt);
			const isDateCorrect =
				runAtDate.toISOString().split('T')[0] === sevenDaysLater.toISOString().split('T')[0];
			expect(isDateCorrect).toBe(true);
		} else {
			throw new Error("No operation with status 'pending' found.");
		}
		// Validate that the email message was sent to the queue
		expect(sendMock).toHaveBeenCalledTimes(1);
		expect(sendMock).toHaveBeenCalledWith({
			userId: 'u_testuserid',
			templateName: 'WEEKLY_EARNINGS',
		});
	});
});
