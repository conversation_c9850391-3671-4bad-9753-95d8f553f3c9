import { describe, expect, it, beforeEach, afterEach, vi } from 'vitest';
import { env, runInDurableObject, runDurableObjectAlarm, fetchMock, SELF } from 'cloudflare:test';
import { UserDataStoreManager } from '../src/durable-object';
import {
	BASIC_AUTH_KEY_ADMIN_USER,
	createGroupContext,
	createUser,
	setupTransactionReleaseMock,
	WORKER_BASE_URL,
} from './mocks';

describe('Worker and durable object integration test for internal APIs', () => {
	beforeEach(() => {
		fetchMock.activate();
		createUser(
			env.CLOUDFLARE_INTEGRATION_USER_USERNAME,
			env.CLOUDFLARE_INTEGRATION_USER_PASSWORD,
			'u_abcd'
		);
	});

	afterEach(() => {
		fetchMock.deactivate();
	});

	it('should return error for missing authentication on POST', async () => {
		const resp = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/123/contexts/latest/assignments`,
			{
				method: 'POST',
				body: JSON.stringify({ operation: 'add' }),
			}
		);
		expect(resp.status).toBe(401);
	});

	it('should return 200 and update user data for a group on POST', async () => {
		await createGroupContext('123', { groupId: '456', type: '5for5' });
		const validPayload = {
			groupId: '456',
			platformId: 'cint',
			assignmentId: 'ta_asdf32143213212211',
		};

		const postResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/456/contexts/latest/assignments`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': '123',
				},
				body: JSON.stringify(validPayload),
			}
		);
		const responseData = (await postResponse.json()) as any;
		expect(responseData.success).toBe(true);
		expect(responseData.data.contextData.completedAssignments.length).toBeGreaterThan(0);

		const getResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/456/contexts/latest`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': '123',
				},
			}
		);
		expect(getResponse.status).toBe(200);
		const data = await getResponse.json();
		expect(data).toStrictEqual(responseData);
	});

	it('should return 200 on POST /task_groups/:groupId/contexts with valid payload', async () => {
		const postResponse = await createGroupContext('u_123abc', { groupId: 'gr_123', type: '5for5' });
		const responseData = (await postResponse.json()) as any;
		expect(postResponse.status).toBe(200);
		expect(responseData.success).toBe(true);

		// Verify the update via GET request
		const getResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/gr_123/contexts/latest`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'u_123abc',
				},
			}
		);

		expect(getResponse.status).toBe(200);
		const { data } = (await getResponse.json()) as any;
		expect(data).toEqual(
			expect.objectContaining({
				contextData: {
					completedAssignments: [],
				},
				groupId: 'gr_123',
				status: 'active',
				type: '5for5',
			})
		);
	});

	it('should return 400 when attempting group context create if an active group context is already available', async () => {
		const postResponse = await createGroupContext('u_123abcd', {
			groupId: 'gr_123',
			type: '5for5',
		});
		const responseData = (await postResponse.json()) as any;
		expect(postResponse.status).toBe(200);
		expect(responseData.success).toBe(true);

		// Verify the update via GET request
		const getResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/gr_123/contexts/latest`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'u_123abcd',
				},
			}
		);

		expect(getResponse.status).toBe(200);
		const { data } = (await getResponse.json()) as any;
		expect(data).toEqual(
			expect.objectContaining({
				contextData: {
					completedAssignments: [],
				},
				groupId: 'gr_123',
				status: 'active',
				type: '5for5',
			})
		);
		const postResponse2 = await createGroupContext('u_123abcd', {
			groupId: 'gr_123',
			type: '5for5',
		});
		const responseData2 = (await postResponse2.json()) as any;

		expect(postResponse2.status).toBe(400);
		expect(responseData2).toEqual(
			expect.objectContaining({
				detail: [
					{
						errorCode: 'ACTIVE_GROUP_CONTEXT_ALREADY_EXISTS',
						message: 'Active group context already exists',
					},
				],
			})
		);
	});

	it('should return 204 on group context delete', async () => {
		const postResponse = await createGroupContext('u_blehh', { groupId: 'gr_123', type: '5for5' });
		const responseData = (await postResponse.json()) as any;
		expect(postResponse.status).toBe(200);
		expect(responseData.success).toBe(true);

		const validPayload = {
			groupId: 'gr_123',
			platformId: 'cint',
			assignmentId: 'ta_asdf32143213212211',
		};

		const postResponseAssignments = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/gr_123/contexts/latest/assignments`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': 'u_blehh',
				},
				body: JSON.stringify(validPayload),
			}
		);
		const responseData2 = (await postResponseAssignments.json()) as any;
		expect(responseData2.success).toBe(true);
		expect(responseData2.data.contextData.completedAssignments.length).toBeGreaterThan(0);

		const getResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/gr_123/contexts/latest`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'u_blehh',
				},
			}
		);
		expect(getResponse.status).toBe(200);
		const data = (await getResponse.json()) as any;

		const deleteResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/gr_123/contexts/${data.data.id}`,
			{
				method: 'DELETE',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'u_blehh',
				},
			}
		);
		expect(deleteResponse.status).toBe(204);
	});

	it('should update the rewardId with patch groups/:groupId/contexts/:contextId', async () => {
		const postResponse = await createGroupContext('u_blehh2', { groupId: 'gr_123', type: '5for5' });
		const responseData = (await postResponse.json()) as any;
		expect(postResponse.status).toBe(200);
		expect(responseData.success).toBe(true);

		const validPayload = {
			groupId: 'gr_123',
			platformId: 'cint',
			assignmentId: 'ta_asdf32143213212211',
		};

		const postResponseAssignments = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/gr_123/contexts/latest/assignments`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': 'u_blehh2',
				},
				body: JSON.stringify(validPayload),
			}
		);
		const responseData2 = (await postResponseAssignments.json()) as any;
		expect(responseData2.success).toBe(true);
		expect(responseData2.data.contextData.completedAssignments.length).toBeGreaterThan(0);

		const getResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/gr_123/contexts/latest`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'u_blehh2',
				},
			}
		);
		expect(getResponse.status).toBe(200);
		const data = (await getResponse.json()) as any;

		const patchResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/gr_123/contexts/${data.data.id}`,
			{
				method: 'PATCH',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'u_blehh2',
				},
				body: JSON.stringify({ rewardId: 'r_rewardId' }),
			}
		);
		expect(patchResponse.status).toBe(200);
		const { data: patchData } = (await patchResponse.json()) as any;
		expect(patchData.rewardId).toBe('r_rewardId');
	});

	it('should return 404 for valid GET if data is not found', async () => {
		const resp = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/123/contexts/latest`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': '123',
				},
			}
		);
		expect(resp.status).toBe(404);
		const data = await resp.json();

		expect(data).toEqual(
			expect.objectContaining({
				detail: [
					{
						errorCode: 'GROUP_CONTEXT_NOT_FOUND',
						message: 'Group context not found',
					},
				],
				instance: '/cf/v1/internal/task_groups/123/contexts/latest',
				method: 'GET',
				path: '/cf/v1/internal/task_groups/123/contexts/latest',
				status: 404,
			})
		);
	});

	it('should not allow updating group context to status other than completed', async () => {
		await createGroupContext('user_444', { groupId: 'group_123', type: '5for5' });
		const validPayload = {
			groupId: 'group_123',
			platformId: 'cint',
			assignmentId: 'ta_asdf32143213212211',
		};

		const postAssignmentsResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/group_123/contexts/latest/assignments`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': 'user_444',
				},
				body: JSON.stringify(validPayload),
			}
		);
		const responseData = (await postAssignmentsResponse.json()) as any;
		expect(responseData.success).toBe(true);
		expect(responseData.data.contextData.completedAssignments.length).toBeGreaterThan(0);

		const patchGroupContextToCompleteGroupContextResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/group_123/contexts/latest`,
			{
				method: 'PATCH',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': 'user_444',
				},
				body: JSON.stringify({
					status: 'pending-payment',
				}),
			}
		);
		expect(patchGroupContextToCompleteGroupContextResponse.status).toBe(400);
		expect((await patchGroupContextToCompleteGroupContextResponse.json()) as any).toEqual(
			expect.objectContaining({
				detail: [
					{
						errorCode: 'ZOD_VALIDATION_ERROR',
						message:
							"Path: status. Message: Invalid enum value. Expected 'completed' | 'expired', received 'pending-payment'",
					},
				],
				instance: '/cf/v1/internal/task_groups/:groupId/contexts/latest',
				method: 'PATCH',
				path: '/cf/v1/internal/task_groups/:groupId/contexts/latest',
				status: 400,
				title: 'Invalid request data',
				type: 'Validation Error',
			})
		);
	});

	it('should mark the group context as pending payment once the 5th assignment completion request is completed', async () => {
		await createGroupContext('45678', { groupId: '123', type: '5for5' });
		const basePayload = {
			groupId: '123',
			platformId: 'cint',
		};

		const assignmentIds = [
			'ta_asdf32143213212212',
			'ta_asdf32143213212213',
			'ta_asdf32143213212214',
			'ta_asdf32143213212215',
			'ta_asdf32143213212216',
		];

		for (const assignmentId of assignmentIds) {
			const insertAssignmentResponse = await SELF.fetch(
				`${WORKER_BASE_URL}/cf/v1/internal/task_groups/123/contexts/latest/assignments`,
				{
					method: 'POST',
					headers: {
						'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
						'Content-Type': 'application/json',
						'X-Impersonated-UserId': '45678',
					},
					body: JSON.stringify({
						...basePayload,
						assignmentId,
					}),
				}
			);
			expect(insertAssignmentResponse.status).toBe(200);
		}

		const getResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/123/contexts/latest`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': '45678',
				},
			}
		);
		const { data } = (await getResponse.json()) as any;
		expect(data.status).toBe('pending-payment');

		const patchGroupContextToCompleteGroupContextResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/123/contexts/latest`,
			{
				method: 'PATCH',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': '45678',
				},
				body: JSON.stringify({
					status: 'completed',
				}),
			}
		);
		const { data: completionData } =
			(await patchGroupContextToCompleteGroupContextResponse.json()) as any;
		expect(completionData.status).toBe('completed');
	});

	it('should restrict marking as completed unless the group context is in the final state', async () => {
		await createGroupContext('user_999', { groupId: 'group_123', type: '5for5' });
		const basePayload = {
			groupId: 'group_123',
			platformId: 'cint',
		};

		const assignmentIds = [
			'ta_asdf32143213212212',
			'ta_asdf32143213212213',
			'ta_asdf32143213212214',
			'ta_asdf32143213212215',
		];

		for (const assignmentId of assignmentIds) {
			const insertAssignmentResponse = await SELF.fetch(
				`${WORKER_BASE_URL}/cf/v1/internal/task_groups/group_123/contexts/latest/assignments`,
				{
					method: 'POST',
					headers: {
						'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
						'Content-Type': 'application/json',
						'X-Impersonated-UserId': 'user_999',
					},
					body: JSON.stringify({
						...basePayload,
						assignmentId,
					}),
				}
			);
			expect(insertAssignmentResponse.status).toBe(200);
		}

		const getResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/group_123/contexts/latest`,
			{
				method: 'GET',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'user_999',
				},
			}
		);
		const { data } = (await getResponse.json()) as any;
		expect(data.status).toBe('active');

		const patchGroupContextToCompleteGroupContextResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/group_123/contexts/latest`,
			{
				method: 'PATCH',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': 'user_999',
				},
				body: JSON.stringify({
					status: 'completed',
				}),
			}
		);
		expect(patchGroupContextToCompleteGroupContextResponse.status).toBe(400);

		expect(await patchGroupContextToCompleteGroupContextResponse.json()).toEqual(
			expect.objectContaining({
				detail: [
					{
						errorCode: 'CANNOT_MARK_AS_COMPLETED',
						message: "Cannot mark group context as completed when current status is 'active'",
					},
				],
				instance: '/cf/v1/internal/task_groups/group_123/contexts/latest',
				method: 'PATCH',
				path: '/cf/v1/internal/task_groups/group_123/contexts/latest',
				status: 400,
				title: '',
				type: '',
			})
		);
	});

	// it('should roll back any other database writes if any one of them fails', async () => {
	// 	await createGroupContext('user_rollback', { groupId: 'group_123', type: '5for5' });
	// 	const basePayload = {
	// 		groupType: '5for5',
	// 		groupId: 'group_123',
	// 		platformId: 'cint',
	// 		status: 'completed',
	// 	};

	// 	const insertAssignmentResponse = await SELF.fetch(
	// 		`${WORKER_BASE_URL}/cf/v1/internal/task_groups/group_3for3/contexts/latest/assignments`,
	// 		{
	// 			method: 'POST',
	// 			headers: {
	// 				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
	// 				'Content-Type': 'application/json',
	// 				'X-Impersonated-UserId': 'user_rollback',
	// 			},
	// 			body: JSON.stringify({
	// 				...basePayload,
	// 				assignmentId: 'ta_assignmentId1',
	// 			}),
	// 		}
	// 	);
	// 	expect(insertAssignmentResponse.status).toBe(200);

	// 	const getResponse = await SELF.fetch(
	// 		`${WORKER_BASE_URL}/cf/v1/internal/task_groups/group_3for3/contexts/latest`,
	// 		{
	// 			method: 'GET',
	// 			headers: {
	// 				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
	// 				'X-Impersonated-UserId': 'user_rollback',
	// 			},
	// 		}
	// 	);
	// 	expect(getResponse.status).toBe(200);
	// 	await createGroupContext('user_rollback', { groupId: 'group_5for5', type: '5for5' });

	// 	const duplicateAssignmentForUserResponse = await SELF.fetch(
	// 		`${WORKER_BASE_URL}/cf/v1/internal/task_groups/group_5for5/contexts/latest/assignments`,
	// 		{
	// 			method: 'POST',
	// 			headers: {
	// 				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
	// 				'Content-Type': 'application/json',
	// 				'X-Impersonated-UserId': 'user_rollback',
	// 			},
	// 			body: JSON.stringify({
	// 				...basePayload,
	// 				assignmentId: 'ta_assignmentId1',
	// 			}),
	// 		}
	// 	);
	// 	expect(duplicateAssignmentForUserResponse.status).toBe(500);

	// 	const getResponseForGroup5For5 = await SELF.fetch(
	// 		`${WORKER_BASE_URL}/cf/v1/internal/task_groups/group_5for5/contexts/latest`,
	// 		{
	// 			method: 'GET',
	// 			headers: {
	// 				'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
	// 				'X-Impersonated-UserId': 'user_rollback',
	// 			},
	// 		}
	// 	);
	// 	expect(getResponseForGroup5For5.status).toBe(404);
	// });

	it('should remove an assignment from the group context and return the updated context', async () => {
		await createGroupContext('user_random', { groupId: 'group_123', type: '5for5' });
		const basePayload = {
			groupId: 'group_123',
			platformId: 'cint',
		};

		const assignmentIdToRemove = 'ta_remove12345';

		const addAssignmentResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/group_123/contexts/latest/assignments`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': 'user_random',
				},
				body: JSON.stringify({
					...basePayload,
					assignmentId: assignmentIdToRemove,
				}),
			}
		);
		expect(addAssignmentResponse.status).toBe(200);
		const { data: addAssignmentData } = (await addAssignmentResponse.json()) as any;
		expect(addAssignmentData.contextData.completedAssignments).toStrictEqual(['ta_remove12345']);

		const deleteAssignmentResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/group_123/contexts/assignments/${assignmentIdToRemove}`,
			{
				method: 'DELETE',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'user_random',
				},
			}
		);
		expect(deleteAssignmentResponse.status).toBe(200);

		const { data } = (await deleteAssignmentResponse.json()) as any;

		expect(data.contextData.completedAssignments).toStrictEqual([]);
		expect(data).not.toContain(assignmentIdToRemove);
	});

	it('should throw error if the group context is not active', async () => {
		await createGroupContext('user_random_2', { groupId: 'group_123', type: '5for5' });
		const basePayload = {
			groupId: 'group_123',
			platformId: 'cint',
		};

		const assignmentIds = [
			'ta_asdf32143213212212',
			'ta_asdf32143213212213',
			'ta_asdf32143213212214',
			'ta_asdf32143213212215',
			'ta_asdf32143213212216',
		];

		// Insert assignments for each assignmentId
		for (const assignmentId of assignmentIds) {
			const insertAssignmentResponse = await SELF.fetch(
				`${WORKER_BASE_URL}/cf/v1/internal/task_groups/group_123/contexts/latest/assignments`,
				{
					method: 'POST',
					headers: {
						'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
						'Content-Type': 'application/json',
						'X-Impersonated-UserId': 'user_random_2',
					},
					body: JSON.stringify({
						...basePayload,
						assignmentId,
					}),
				}
			);
			expect(insertAssignmentResponse.status).toBe(200);
		}

		// Attempt to delete one of the assignments
		const deleteAssignmentResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/group_123/contexts/assignments/${assignmentIds[0]}`,
			{
				method: 'DELETE',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'X-Impersonated-UserId': 'user_random_2',
				},
			}
		);
		expect(deleteAssignmentResponse.status).toBe(400);

		// Check for specific parts of the error response
		const responseJson = await deleteAssignmentResponse.json();
		expect(responseJson).toEqual(
			expect.objectContaining({
				detail: [
					{
						errorCode: 'GROUP_CONTEXT_NOT_ACTIVE',
						message: 'Group context is not active',
					},
				],
				status: 400,
				instance:
					'/cf/v1/internal/task_groups/group_123/contexts/assignments/ta_asdf32143213212212',
				method: 'DELETE',
				path: '/cf/v1/internal/task_groups/group_123/contexts/assignments/ta_asdf32143213212212',
				title: '',
				type: '',
			})
		);
	});
});

describe('Worker and durable object integration test (user group context APIs)', () => {
	beforeEach(() => {
		createUser(
			env.CLOUDFLARE_INTEGRATION_USER_USERNAME,
			env.CLOUDFLARE_INTEGRATION_USER_PASSWORD,
			'u_abcd'
		);
		createUser(
			env.CLOUDFLARE_INTEGRATION_USER_USERNAME,
			env.CLOUDFLARE_INTEGRATION_USER_PASSWORD,
			'u_abcde'
		);
		fetchMock.activate();
	});

	afterEach(() => {
		fetchMock.deactivate();
	});

	it('should return 404 for the user if context is not found', async () => {
		const resp = await SELF.fetch(`${WORKER_BASE_URL}/cf/v1/task_groups/111/contexts/latest`, {
			method: 'GET',
			headers: {
				Authorization: `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
			},
		});
		expect(resp.status).toBe(404);
	});

	it('should return 200 with latest group context with user context from authentication token', async () => {
		await createGroupContext('u_abcd', { groupId: 'group_123', type: '5for5' });
		const validPayload = {
			groupId: 'group_123',
			platformId: 'cint',
			assignmentId: 'ta_asdf32143213212211',
		};

		const postResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/group_123/contexts/latest/assignments`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': 'u_abcd',
				},
				body: JSON.stringify(validPayload),
			}
		);
		expect(postResponse.status).toBe(200);
		const resp = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/task_groups/group_123/contexts/latest`,
			{
				method: 'GET',
				headers: {
					Authorization: `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				},
			}
		);
		const { data } = (await resp.json()) as any;
		expect(resp.status).toBe(200);
		expect(data.contextData.completedAssignments.length).toBeGreaterThan(0);
	});

	it('should return 200 with latest group context with user context from authentication token', async () => {
		const res = await createGroupContext('u_abcd', { groupId: 'group_123', type: '5for5' });
		const validPayload = {
			groupId: 'group_123',
			platformId: 'cint',
			assignmentId: 'ta_asdf32143213212211',
		};
		expect(res.status).toBe(200);
		const postResponse = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/internal/task_groups/group_123/contexts/latest/assignments`,
			{
				method: 'POST',
				headers: {
					'Authorization': `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
					'Content-Type': 'application/json',
					'X-Impersonated-UserId': 'u_abcd',
				},
				body: JSON.stringify(validPayload),
			}
		);
		expect(postResponse.status).toBe(200);
		const resp = await SELF.fetch(
			`${WORKER_BASE_URL}/cf/v1/task_groups/group_123/contexts/latest?enableStats=true`,
			{
				method: 'GET',
				headers: {
					Authorization: `Basic ${BASIC_AUTH_KEY_ADMIN_USER}`,
				},
			}
		);
		expect(resp.status).toBe(200);
		const { data } = (await resp.json()) as any;
		expect(data).toEqual(
			expect.objectContaining({
				contextData: {
					completedAssignments: ['ta_asdf32143213212211'],
					completedCount: 1,
					isFulfilled: false,
					requiredCount: 5,
				},
				groupId: 'group_123',
				status: 'active',
				type: '5for5',
			})
		);
	});
});

describe('Group context alarm test', () => {
	beforeEach(() => {
		fetchMock.activate();
		setupTransactionReleaseMock();
	});

	afterEach(() => {
		fetchMock.deactivate();
	});

	it('should not expire group context', async () => {
		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('u_expiration');
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
		const data = await runInDurableObject(stub, async (instance: UserDataStoreManager, state) => {
			await instance.storeDurableObjectName('u_expiration');
			return await instance.handleCreateGroupContext('groupId_2', {
				type: '2for3',
				groupId: 'groupId_2',
			});
		});
		expect(data.data?.id).toBeDefined();
		const success = await runDurableObjectAlarm(stub);
		expect(success).toBe(true);
		const groupContext = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				return await instance.getAllGroupContexts();
			}
		);
		// since there is no group context that has met expiry date
		expect(groupContext.data![0].status).toBe('active');
	});

	it('should expire group context', async () => {
		const id = env.USER_DATA_MANAGER_DURABLE_OBJECT.idFromName('u_expiration2');
		const stub = env.USER_DATA_MANAGER_DURABLE_OBJECT.get(id);
		const data = await runInDurableObject(stub, async (instance: UserDataStoreManager, state) => {
			await instance.storeDurableObjectName('u_expiration2');
			return await instance.handleCreateGroupContext('groupId_2', {
				type: '2for3',
				groupId: 'groupId_2',
			});
		});
		expect(data.data?.id).toBeDefined();
		const afterExpirationTime =
			new Date().getTime() + env.GROUP_CONTEXT_EXPIRY_DAYS * 24 * 60 * 60 * 1000;
		const date = new Date().setTime(afterExpirationTime);
		// setting system time to the date when the group context would expire
		vi.setSystemTime(date);

		const success = await runDurableObjectAlarm(stub);

		expect(success).toBe(true);
		const groupContext = await runInDurableObject(
			stub,
			async (instance: UserDataStoreManager, state) => {
				return await instance.getAllGroupContexts();
			}
		);
		expect(groupContext.data![0].status).toBe('expired');
	});
});
